{"version": 3, "file": "js/resource-application-2021.35bea537.js", "mappings": "oCAAAA,EAAOC,QAAU,CACfC,KAAM,sBACNC,QAAQ,E,mBCFVH,EAAOC,QAAU,CACfC,KAAM,0BACNC,QAAQ,E,mBCEVH,EAAOC,QAAU,CACfC,KAAM,6BACNC,QAAQ,E,mBCDVH,EAAOC,QAAU,CACfC,KAAM,iCACNC,QAAQ,E,mBCFVH,EAAOC,QAAU,CACfC,KAAM,mBACNC,QAAQ,E,mBCHVH,EAAOC,QAAU,CACfC,KAAM,qBACNC,QAAQ,E,mBCDVH,EAAOC,QAAU,CACfC,KAAM,kBACNC,QAAQ,E,mBCHVH,EAAOC,QAAU,CACfC,KAAM,mBACNC,QAAQ,E,mBCDVH,EAAOC,QAAU,CACfC,KAAM,oBACNC,QAAQ,E,mBCHVH,EAAOC,QAAU,CACfC,KAAM,iCACNC,QAAQ,E,mBCDVH,EAAOC,QAAU,CACfC,KAAM,8BACNC,QAAQ,E,mBCFVH,EAAOC,QAAU,CACfC,KAAM,+BACNC,QAAQ,E,iDCPNC,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,EAAE,EAChJG,EAAkB,GCMtB,GACEC,MAAO,CAELC,OAAOC,EAAIC,GACT,IAAIC,EAAe,CAAC,EAChBF,EAAGG,MAAMC,WACXF,EAAaE,SAAWJ,EAAGG,MAAMC,UAE/B,kBAAkBC,KAAKL,EAAGM,QAC5BJ,EAAaK,aAAe,WAE1B,kBAAkBF,KAAKL,EAAGM,QAC5BJ,EAAaK,aAAe,WAE1B,aAAaF,KAAKL,EAAGM,QACvBJ,EAAaK,aAAe,MAE9BhB,KAAKiB,OAAOC,OAAO,aAAcP,EACnC,ICzBgY,I,UCQhYQ,GAAY,OACd,EACArB,EACAQ,GACA,EACA,KACA,KACA,MAIF,EAAea,EAAiB,Q,sCChBhC,G,gBAAeV,IACb,IAAIW,EAAS,GACTC,EAAUZ,EAAGY,QAEjBA,EAAQC,QAAQC,SAAQC,IACtB,IAAIC,EAAQD,EAAQE,KAAKD,MACzBA,GAASL,EAAOO,KAAKF,EAArB,IAGF,IAAIA,EAAQL,EAAOQ,KAAK,OACxBC,SAASJ,MAAQA,CAVnB,GCDA,EAAeK,IACbA,EAAOC,UAAUC,EADnB,E,UCEAC,EAAAA,WAAAA,IAAQC,EAAAA,GACR,MAAMJ,EAAS,IAAII,EAAAA,EAAO,CACxBC,KAAM,OACNC,OAAQ,KAGVC,EAAMP,GAEN,Q,uCCVA,MAAMQ,EAAe,IAEfC,EAAQ,CACZC,QAAS,IAGLC,EAAU,CACdC,WAAWH,GACT,OAAQI,GACCJ,EAAMC,QAAQI,MAAMJ,GAAYA,EAAQ5C,OAAS+C,KAAa,CAAC,CAEzE,EACDE,eAAeC,EAAGL,GAChB,OAAQE,IACN,MAAMH,EAAUC,EAAQC,WAAWC,GACnC,OAAOH,EAAUA,EAAQO,KAAO,EAAhC,CAEH,GAGGC,EAAY,CAChBC,eAAeV,EAAOW,GAEpBA,EAAQC,YAAa,IAAIC,MAAOC,UAEhC,MAAMb,EAAUD,EAAMC,QAAQI,MAAMU,GAC3BA,EAAK1D,OAASsD,EAAQtD,MAAQ2D,OAAOC,OAAOF,EAAMJ,MAE1DV,GAAWD,EAAMC,QAAQb,KAAKuB,EAChC,GAGGO,EAAU,CACRC,eAAN,EAA0Cf,GAAU,+BAA7BzB,EAA6B,EAA7BA,OAAQuB,EAAqB,EAArBA,QAC7B,MAAMD,EAAUC,EAAQC,WAAWC,GACnC,GAAIH,IAAW,IAAIY,MAAOC,UAAYb,EAAQW,YAAcb,EAE1D,MAAO,EAAC,EAAME,GAEdtB,EAAO,iBAAkB,CAAEtB,KAAM+C,EAAUgB,OAAQ,UAAWZ,KAAM,KANpB,cAStBa,EAAAA,EAAAA,aAAyB,CACnDC,OAAQ,qCACRC,OAAQ,CAACnB,KAXuC,eAS3CgB,EAT2C,KASnCI,EATmC,KAuBlD,OAVIJ,GACFzC,EAAO,iBAAkB,CACvBtB,KAAM+C,EACNgB,OAAQ,SACRZ,KAAMgB,EAAIC,OAAOjB,KAAKkB,KAAKX,IAAD,CACxBY,MAAO,GAAKZ,EAAKa,YACjBC,MAAOd,EAAKe,kBAIX,CAACV,EAAQlB,EAAQC,WAAWC,GAvBe,KAwBnD,GAGH,OACEJ,QACAS,YACAS,UACAhB,WChEF,MAAMH,EAAe,IAEfC,EAAQ,CACZ+B,YAAa,IAGT7B,EAAU,CACd8B,cAAchC,GAEZ,OAAQiC,GACCjC,EAAM+B,YAAY1B,MAAM6B,GAAeA,EAAW7E,OAAS4E,KAAmB,CAAC,CAEzF,EACDE,kBAAkB5B,EAAGL,GAEnB,OAAQ+B,IACN,MAAMC,EAAahC,EAAQ8B,cAAcC,GACzC,OAAOC,EAAaA,EAAW1B,KAAO,CAAtC,CAEH,EACD4B,cAAc7B,EAAGL,GAEf,OAAQ+B,IACN,MAAMC,EAAahC,EAAQ8B,cAAcC,GACzC,OAAQtB,IACN,IAAI0B,EAASH,EAAW1B,KACpB8B,EAAM,EAEV,OAAe,GAAXD,IAEJ1B,EAAU4B,KAAKC,IAAI7B,EAAS,GAAK,EAE7BA,EAAU,GACR0B,GAAU,YACZA,EAASI,SAASJ,EAAS,YAC3B1B,GAAoB,GACpB2B,EAAMD,IAAY1B,EAAU,GAE5B2B,EAAM,EAGRA,EAAMD,IAAY1B,EAAU,KAGpBA,IAAY+B,MAAM/B,EAAU,IAAe,EAAN2B,GAA/C,CApBF,CAuBH,EACDK,sBAAsBpC,EAAGL,GAGvB,OAAQ+B,IACN,MAAMC,EAAahC,EAAQ8B,cAAcC,GACnCG,EAAgBlC,EAAQkC,cAAcH,GAC5C,OAAQtB,IACqB,GAApBuB,EAAW1B,MAAc4B,EAAczB,EADhD,CAIH,GAGGF,EAAY,CAChBmC,kBAAkB5C,EAAOW,GAEvBA,EAAQC,YAAa,IAAIC,MAAOC,UAEhC,MAAMoB,EAAalC,EAAM+B,YAAY1B,MAAMU,GAClCA,EAAK1D,OAASsD,EAAQtD,MAAQ2D,OAAOC,OAAOF,EAAMJ,MAE1DuB,GAAclC,EAAM+B,YAAY3C,KAAKuB,EACvC,GAGGO,EAAU,CACR2B,6BAAN,EAAwDZ,GAAgB,+BAAnCtD,EAAmC,EAAnCA,OAAQuB,EAA2B,EAA3BA,QAC3C,MAAMgC,EAAahC,EAAQ8B,cAAcC,GACzC,GAAIC,IAAc,IAAIrB,MAAOC,UAAYoB,EAAWtB,YAAcb,EAEhE,MAAO,EAAC,EAAMmC,GAEdvD,EAAO,oBAAqB,CAAEtB,KAAM4E,EAAgBb,OAAQ,UAAWZ,KAAM,KANT,cAS1Ca,EAAAA,EAAAA,aAAyB,CACnDC,OAAQ,0DACRC,OAAQ,CAAC,KAAMU,KAXqD,eAS/Db,EAT+D,KASvDI,EATuD,KAoBtE,OAPIJ,GACFzC,EAAO,oBAAqB,CAC1BtB,KAAM4E,EACNb,OAAQ,SACRZ,KAAMgB,EAAIC,OAAOY,SAGd,CAACjB,EAAQlB,EAAQ8B,cAAcC,GApBgC,KAqBvE,GAGH,OACEjC,MADa,EAEbS,UAFa,EAGbS,QAHa,EAIbhB,QAAOA,GCtGT,MAAMF,EAAQ,CACZ8C,YAAa,CAAC,GAGV5C,EAAU,CACd6C,eAAe/C,GAEb,OAAOA,EAAM8C,WACd,EACDA,YAAY9C,GAEV,OAAOA,EAAM8C,WACd,GAGGrC,EAAY,CAChBuC,oBAAoBhD,EAAOW,GACzBX,EAAM8C,YAAcnC,CACrB,GAGGO,EAAU,CACR+B,mBAAN,GAA8C,+BAAnBtE,EAAmB,EAAnBA,OAAQuB,EAAW,EAAXA,QAAW,cAChBmB,EAAAA,EAAAA,aAAyB,CACnDC,OAAQ,2BACRC,OAAQ,KAHkC,eACrCH,EADqC,KAC7BI,EAD6B,KAQ5C,OAHIJ,GACFzC,EAAO,sBAAuB6C,EAAIC,OAAOjB,MAEpC,CAACY,EAAQlB,EAAQ6C,eARoB,KAS7C,GAGH,OACE/C,MADa,EAEbS,UAFa,EAGbS,QAHa,EAIbhB,QAAOA,GClCTR,EAAAA,WAAAA,IAAQwD,EAAAA,IAER,UAAmBA,EAAAA,GAAAA,MAAW,CAC5BC,QAAS,CACPC,YADO,EAEPlB,WAFO,EAGPmB,KAAIA,K,kBCVR,MAAMC,EAAS,CAAC,EACVf,GAAOgB,EAAAA,EAAAA,GAAOC,EAAAA,EAAKF,GAEzB,Q,mBCAA5D,EAAAA,WAAAA,IAAQ+D,KAER/D,EAAAA,WAAAA,SAAegE,EAAAA,QAAAA,Q,uCCPf,MAAMJ,EAAS,CACbK,QAAS,IACTC,UAAW,IACXC,OAAQ,GACRC,OAAQ,GACRC,UAAW,GAGN,SAASC,IAA8B,IAAbC,EAAa,uDAAJ,GAExC,GADAA,EAAoB,OAAXA,EAAkB,GAAK,GAAKA,EACjCA,EAAOC,OAASZ,EAAOS,UACzBE,EAASA,EAAOE,MAAMb,EAAOK,SAC7BM,EAAO,GAAKA,EAAO,GAChBE,MAAMb,EAAOM,WACbvE,KAAK,IACL+E,QAAQ,sBAAuBd,EAAOM,WACzCK,EAAO,GAAKA,EAAO,IAAM,GACzBA,EAAO,GACLA,EAAO,GAAGC,OAASZ,EAAOS,UAAYE,EAAO,GAAGlF,MAAM,EAAGuE,EAAOS,WAAaE,EAAO,GAElFA,EAAO,IAAM,EACfA,EAAO,GAAKX,EAAOK,QAAUM,EAAO,GAEpCA,EAAO,GAAK,GAEdA,EAASA,EAAO,GAAKA,EAAO,OACvB,IAAe,KAAXA,EACT,MAAO,GAEPA,EAASA,EAAOE,MAAMb,EAAOK,SAC7BM,EAAO,GAAKA,EAAO,IAAM,GACzBA,EAAO,GACLA,EAAO,GAAGC,OAASZ,EAAOS,UAAYE,EAAO,GAAGlF,MAAM,EAAGuE,EAAOS,WAAaE,EAAO,GAElFA,EAAO,IAAM,IACfA,EAAO,GAAKX,EAAOK,QAAUM,EAAO,IAEtCA,EAASA,EAAO,GAAKA,EAAO,EAC7B,CACD,OAAOX,EAAOO,OAASI,EAASX,EAAOQ,MACxC,CCpCDO,EAAAA,WAAAA,OAAW,WAAYC,IACrB,GAAY,OAARA,EAAc,MAAO,GACzB,GAAY,KAARA,EAAY,MAAO,GACvB,MAAM9D,EAAO+D,OAAOD,GACpB,OAAO/B,KAAKG,MAAMlC,GAAQ8D,EAAMN,EAAiBxD,EAAjD,IAGF6D,EAAAA,WAAAA,OAAW,WAAYC,IACrB,MAAM9D,EAAO+D,OAAOD,GACpB,OAAO/B,KAAKG,MAAMlC,GAAQ8D,EAAM/B,KAAKiC,MAAMhE,EAA3C,IAGF6D,EAAAA,WAAAA,OAAW,gBAAiBC,IAC1B,MAAM9D,EAAO+D,OAAOD,GACpB,OAAO/B,KAAKG,MAAMlC,GAAQ8D,EAAe,IAAT9D,EAAa,GAAK8D,CAAlD,IAGFD,EAAAA,WAAAA,OAAW,SAAS,SAACC,GAAkC,IAA7BG,EAA6B,uDAAvB,mBAC9B,OAAOH,EAAMI,IAAMJ,GAAKK,OAAOF,GAAO,EACvC,IAEDJ,EAAAA,WAAAA,OAAW,eAAe,CAACC,EAAKlE,KAC9B,IAAIH,EAAUoE,EAAAA,WAAAA,OAAAA,QAAAA,WAA8BjE,GAC5C,IAAKH,EAAS,OAAOqE,EAErB,MAAMM,EAAS3E,EAAQO,KAAKH,MAAMU,GAAS,GAAKA,EAAKY,QAAU,GAAK2C,IACpE,OAAOM,EAASA,EAAO/C,MAAQyC,CAA/B,ICbF,MAAMO,GAAIC,EAAQ,KAIZC,GAAgBD,EAAAA,MAWP,SAASE,GAAT,GAAmC,IAAlBC,EAAkB,EAAlBA,OAAQpF,EAAU,EAAVA,OAgBtC,OAfAqF,OAAO3C,KAAOA,EACd2C,OAAOL,EAAIA,GAEXhF,GAAUN,EAAAA,UAAiBM,GAE3BoF,GACEjE,OAAOmE,KAAKF,GAAQvD,KAAKrE,IACvB+H,EAAMC,eAAehI,EAAM4H,EAAO5H,GAAlC,IAIJgH,EAAAA,WAAAA,KAAWA,EAAAA,WAAAA,UAAAA,KAAqB,IAAIA,EAAAA,WACpCA,EAAAA,WAAAA,QAAc9E,EACd8E,EAAAA,WAAAA,OAAae,EAEN,CACLA,MADK,EAEL7F,OAAMA,EAET,CA9BDwF,GAAcI,OAAOzD,KAAKlD,IACxB,MAAM8G,EAAOP,GAAcvG,GAC3B,GAAI8G,EAAKhI,OAAQ,CACf,MAAMiI,EAAgB/G,EAAK4F,QAAQ,8BAA+B,MAClEC,EAAAA,WAAAA,UAAciB,EAAKjI,MAAOmI,GACxBV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAE,YAAcS,gBAAjB,6CAEV,KC1BH7F,EAAAA,WAAAA,OAAW,gBAAgB,CAACiC,EAAO8C,IAC1BI,EAAErG,KAAK,CAAC,oBAAqB,cAAemD,IAAU,OAG/DjC,EAAAA,WAAAA,OAAW,aAAciC,IACvB,MAAMD,EAAM,CACV,EAAG,KACH,EAAG,KACH,EAAG,QAEL,OAAOA,EAAIC,EAAX,ICJF0C,EAAAA,WAAAA,OAAAA,eAA2B,E,SAEDW,GAAM,CAC9BC,OAD8B,IAE9BpF,OAAMA,MAFAuF,GAAAA,GAAAA,MAAO7F,GAAAA,GAAAA,OAKf,IAAI8E,EAAAA,WAAI,CACNe,MADM,GAEN7F,OAFM,GAGNhC,OAASkI,GAAMA,EAAEC,KAChBC,OAAO,O,8EChBV,MAAMC,EAAepF,IACnB,IAAIqF,EAAO,CACTC,MAAOtF,EAAKsF,MACZC,SAAUvF,EAAKuF,SACfC,cAAexF,EAAKwF,cACpBC,gBAAiBzF,EAAKyF,gBACtBC,WAAY1F,EAAK0F,WACjBC,YAAa3F,EAAK2F,YAClBC,UAAW5F,EAAK4F,UAChBC,WAAY7F,EAAK6F,WACjBC,aAAc9F,EAAK8F,cA+BrB,MA7B0B,YAAtB9F,EAAK/B,cACPoH,EAAKU,UAAY/F,EAAK+F,UACtBV,EAAKW,QAAUhG,EAAKgG,QACpBX,EAAKY,UAAYjG,EAAKiG,UACtBZ,EAAKa,aAAelG,EAAKkG,aACzBb,EAAKc,iBAAmBnG,EAAKmG,iBAC7Bd,EAAKe,eAAiBpG,EAAKoG,eACvBpG,EAAKqG,aAAYhB,EAAKgB,WAAarG,EAAKqG,YAE5ChB,EAAKiB,UAAYtG,EAAKsG,UACtBjB,EAAKkB,cAAgBvG,EAAKuG,cAC1BlB,EAAKmB,YAAcxG,EAAKwG,YACxBnB,EAAKoB,cAAgBzG,EAAKyG,eACI,OAAtBzG,EAAK/B,cACboH,EAAKW,QAAUhG,EAAKgG,QACpBX,EAAKY,UAAYjG,EAAKiG,UAClBjG,EAAKqG,aAAYhB,EAAKgB,WAAarG,EAAKqG,YAI5ChB,EAAKoB,cAAgBzG,EAAKyG,gBAE1BpB,EAAKqB,YAAc1G,EAAK0G,YAExBrB,EAAKsB,cAAgB3G,EAAK2G,cAC1BtB,EAAKoB,cAAgBzG,EAAKyG,cAC1BpB,EAAKe,eAAiBpG,EAAKoG,gBAEzBpG,EAAK4G,KAAIvB,EAAKuB,GAAK5G,EAAK4G,IACrBvB,CAAP,EAGF,MAAMwB,EACJC,mBAA4B,IAAX9G,EAAW,uDAAJ,CAAC,EACvB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAS,MAA2B,YAAtBd,EAAK/B,aAA6B,UAAkC,OAAtB+B,EAAK/B,aAAuB,KAAK,+BAC7F8C,OAAQ,CAACf,EAAK4G,GAAI5G,EAAKkH,SAAUlH,EAAKlC,YAG3C,CAEDqJ,uBAAgC,IAAXnH,EAAW,uDAAJ,CAAC,EAC3B,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAQ,yCACRC,OAAQ,CAACf,EAAKsF,MAAOtF,EAAKoH,UAAWpH,EAAK4F,aAG/C,CAEDyB,gBAAyB,IAAXrH,EAAW,uDAAJ,CAAC,EACpB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAS,MAA2B,YAAtBd,EAAK/B,aAA6B,UAAkC,OAAtB+B,EAAK/B,aAAuB,KAAK,6BAC7F8C,OAAQ,CAACqE,EAAYpF,GAAOA,EAAKlC,YAGtC,CAEDwJ,qBAA8B,IAAXtH,EAAW,uDAAJ,CAAC,EACzB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAS,MAA2B,YAAtBd,EAAK/B,aAA6B,UAAkC,OAAtB+B,EAAK/B,aAAuB,KAAK,yBAC3F+B,EAAKc,SAEPC,OAAQ,CAACqE,EAAYpF,GAAOA,EAAKuH,OAAQvH,EAAKlC,SAAUkC,EAAKwH,aAGlE,CAEDC,iBAA0B,IAAXzH,EAAW,uDAAJ,CAAC,EACrB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAS,MAA2B,YAAtBd,EAAK/B,aAA6B,UAAmC,OAAtB+B,EAAK/B,aAAuB,KAAK,yBAC5F+B,EAAKc,SAEPC,OAAQ,CACN,CACE6F,GAAI5G,EAAK4G,OAKlB,CAEDc,eAAwB,IAAX1H,EAAW,uDAAJ,CAAC,EACnB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAS,MAA2B,YAAtBd,EAAK/B,aAA6B,UAAmC,OAAtB+B,EAAK/B,aAAuB,KAAK,yBAAyB+B,EAAKc,SAC5HC,OAAQ,CAAE,CAAE6F,GAAI5G,EAAK4G,IAAM,QAAS5G,EAAK2H,QAAS3H,EAAKlC,SAAUkC,EAAKwH,aAG3E,CAEDI,gBAAyB,IAAX5H,EAAW,uDAAJ,CAAC,EACpB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAS,MAA2B,YAAtBd,EAAK/B,aAA6B,UAAmC,OAAtB+B,EAAK/B,aAAuB,KAAK,yBAAyB+B,EAAKc,SAC5HC,OAAQ,CAAE,CAAE6F,GAAI5G,EAAK4G,IAAM5G,EAAK2H,QAAS3H,EAAKlC,SAAUkC,EAAKwH,aAGlE,CAEDK,kBAA2B,IAAX7H,EAAW,uDAAJ,CAAC,EACtB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAO,MAA2B,YAAtBgC,EAAK/B,aAA6B,UAAmC,OAAtB+B,EAAK/B,aAAuB,KAAK,6BAC5F+I,YAAa,OACbhH,KAAM,CACJ4G,GAAI5G,EAAK4G,KAGd,CAEDkB,oBAA6B,IAAX9H,EAAW,uDAAJ,CAAC,EACxB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,0DACRC,OAAQ,CAACf,EAAKlC,SAAUkC,EAAK+H,YAC7BnB,GAAI,IAGT,CAEDoB,iBAA0B,IAAXhI,EAAW,uDAAJ,CAAC,EACrB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,wCACRC,OAAQ,CAACf,EAAK+F,UAAW/F,EAAKsF,OAC9BsB,GAAI,IAGT,CAEDqB,iBAA0B,IAAXjI,EAAW,uDAAJ,CAAC,EACrB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,wCACRC,OAAQ,CAACf,EAAK+F,UAAW/F,EAAKsF,OAC9BsB,GAAI,IAGT,CAEDsB,sBAA+B,IAAXlI,EAAW,uDAAJ,CAAC,EAC1B,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,6CACRC,OAAQ,CAACf,EAAK+F,UAAW/F,EAAKsF,MAAOtF,EAAKgG,SAC1CY,GAAI,IAGT,CAEDuB,4BAAqC,IAAXnI,EAAW,uDAAJ,CAAC,EAChC,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,mDACRC,OAAQ,CAACf,EAAK+F,UAAW/F,EAAKsF,OAC9BsB,GAAI,IAGT,CAEDwB,gBAAyB,IAAXpI,EAAW,uDAAJ,CAAC,EACpB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,2CACRC,OAAQ,CAACf,EAAK+F,UAAW/F,EAAKsF,OAC9BsB,GAAI,IAGT,CAEDyB,eAAwB,IAAXrI,EAAW,uDAAJ,CAAC,EACnB,MAAMsI,EAAYtI,EAAKuI,UACnBrE,IAAMlE,EAAKuI,UAAU,IAAIpE,OAAO,uBAChC,GACEqE,EAAUxI,EAAKuI,UAAYrE,IAAMlE,EAAKuI,UAAU,IAAIpE,OAAO,uBAAyB,GAE1F,OAAO4C,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAO,MAA2B,YAAtBgC,EAAK/B,aAA6B,UAAmC,OAAtB+B,EAAK/B,aAAuB,KAAK,kBAAkB+B,EAAKc,YACnHkG,YAAa,OACbhH,KAAM,CACJyI,MAAOzI,EAAKyI,MACZC,OAAQ1I,EAAK2I,KAAO,GAAK3I,EAAKyI,MAC9BG,MAAO,KACPC,UAAW,OACXrD,cAAexF,EAAK8I,SACpBC,OAAQ/I,EAAK+I,OACbzD,MAAOtF,EAAKsF,MACZW,UAAWjG,EAAKiG,UAChBV,SAAUvF,EAAKoH,UACf4B,eAAgBV,EAChBW,aAAcT,EACd1K,SAAUkC,EAAKlC,WAGpB,CAEDoL,mBAA4B,IAAXlJ,EAAW,uDAAJ,CAAC,EACvB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAS,MACe,YAAtBd,EAAK/B,aAA6B,UAAmC,OAAtB+B,EAAK/B,aAAuB,KAAK,iDAElF8C,OAAQ,CAACf,EAAK4G,IACdA,GAAI,IAGT,CAEDuC,mBAA4B,IAAXnJ,EAAW,uDAAJ,CAAC,EACvB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAS,MACe,YAAtBd,EAAK/B,aAA6B,UAAkC,OAAtB+B,EAAK/B,aAAuB,KAAK,+CAEjF8C,OAAQ,CAACf,EAAK4G,IACdA,GAAI,IAGT,CAEDwC,YAAqB,IAAXpJ,EAAW,uDAAJ,CAAC,EAChB,MAAkB,OAAdA,EAAKqJ,MACAC,EAAAA,EAAAA,GAAS,CACdtL,KAAM,iCACN+C,OAAQ,CACN6F,GAAI5G,EAAK4G,MAGU,QAAd5G,EAAKqJ,MACPC,EAAAA,EAAAA,GAAS,CACdtL,KAAM,iCACN+C,OAAQ,CACN6F,GAAI5G,EAAK4G,WAJR,CAQR,CAED2C,cAAuB,IAAXvJ,EAAW,uDAAJ,CAAC,EAClB,OAAOsJ,EAAAA,EAAAA,GAAS,CACdtL,KAAM,8BACNgC,KAAM,CACJwJ,YAAaxJ,EAAKwJ,YAClBC,SAAUzJ,EAAKyJ,SACfC,SAAU1J,EAAK0J,SACfC,kBAAmB3J,EAAK2J,kBACxBC,SAAU5J,EAAK4J,WAGpB,CAEDC,gBAAyB,IAAX7J,EAAW,uDAAJ,CAAC,EACpB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAO,8BACPgJ,YAAa,OACbjG,OAAQ,CACNgF,UAAW/F,EAAK8I,SAChBgB,QAAS9J,EAAKoH,UACdnJ,aAAc,CAAC,KAAK8L,QAAQ,GAAK/J,EAAKsF,QAAU,EAAI,eAAiB,iBACrE0E,MAAOhK,EAAK4G,KAGjB,CAEDqD,uBAAgC,IAAXjK,EAAW,uDAAJ,CAAC,EAC3B,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,2CACRC,OAAQ,CAACf,EAAK8I,UACdlC,GAAI,IAGT,EAGH,WAAmBC,C,0DC/WnB,MAAMA,EACJqD,eAAwB,IAAXlK,EAAW,uDAAJ,CAAC,EACnB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJ4G,GAAI,EACJK,QAAS,MACTnG,OAAQ,+BACRC,OAAQ,CAACf,EAAK+F,UAAW,QAG9B,CAEDoE,mBAA4B,IAAXnK,EAAW,uDAAJ,CAAC,EACvB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CACJiH,QAAS,MACTnG,OAAQ,0DACRC,OAAQ,CAACf,EAAK+F,UAAW/F,EAAKsF,OAC9BsB,GAAI,IAGT,CAEDwD,sBAA+B,IAAXpK,EAAW,uDAAJ,CAAC,EAM1B,OALIA,EAAKqK,QACPrK,EAAKqK,QAAUrK,EAAKqK,QAAQzG,QAAQ,MAAO,KAE3C5D,EAAKqK,QAAU,IAEVtD,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,MACR9C,KAAM,2CACNgJ,YAAa,OACbjG,OAAQ,CACNuJ,YAAatK,EAAKsK,YAClBC,WAAa,uBAAsBvK,EAAKqK,YAG7C,CAEDG,qBAA8B,IAAXxK,EAAW,uDAAJ,CAAC,EACzB,OAAO+G,EAAAA,EAAAA,GAAI,CACTjG,OAAQ,OACR9C,KAAM,kCACNgJ,YAAa,OACbhH,KAAM,CACJ0I,MAAO1I,EAAKyK,UAAYzK,EAAK2I,KAAO,GACpCF,MAAOzI,EAAKyK,SACZC,UAAW,EACXC,aAAc3K,EAAK4K,QACnB7E,UAAW/F,EAAK+F,UAChBF,WAAY7F,EAAK6F,WACjBjF,OAAQ,EACR0E,MAAOtF,EAAKsF,MACZuF,QAAS7K,EAAK6K,QACdN,WAAY,kBACZO,WAAY,IACZC,eAAgB,IAChBC,OAAQhL,EAAKoH,UACb6D,UAAWjL,EAAKoH,UAChB8D,UAAW,EACXtC,MAAO,KACPC,UAAW,SAGhB,EAGH,WAAmBhC,C,sGC5EnB,GACED,GAAI,GACJuE,MAAO,GACP7F,MAAO,GACP8B,UAAW,GACX0B,SAAU,KACVsC,WAAY,GACZC,WAAY,EACZtF,UAAW,GACXL,WAAY,GACZC,YAAa,GAEbK,QAAS,GACTsF,qBAAsB,GACtBrF,UAAW,GACXsF,cAAe,GACfC,UAAW,GACXC,YAAa,GACbC,aAAc,GACdC,aAAc,GACdC,cAAe,GACfC,UAAW,GACXC,aAAc,GACdC,gBAAiB,GACjBC,kBAAmB,GAEnBpG,UAAW,GACXqG,kBAAmB,GACnBC,oBAAqB,GACrBC,qBAAsB,GACtBC,gBAAiB,GACjBC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,mBAAoB,GACpBC,yBAA0B,GAC1BC,qBAAsB,GACtBC,oBAAqB,GAErBC,iBAAkB,GAClBC,oBAAqB,GACrBC,qBAAsB,GACtBC,sBAAuB,GACvBC,aAAc,GAEdC,kBAAmB,GACnBC,aAAc,GACdC,qBAAsB,GACtBC,yBAA0B,GAC1BC,aAAc,GACdC,oBAAqB,GACrBC,UAAW,GACXC,aAAc,GACdC,cAAe,GACfC,WAAY,GACZC,gBAAiB,GACjBC,mBAAoB,GACpBC,gBAAiB,GACjBC,cAAe,GACfC,cAAe,GACfC,kBAAmB,GAGnBC,WAAY,IAAI5N,KAChB6N,oBAAqB,IACrBhH,SAAU,GACViH,eAAgB,GAChBC,YAAa,GACb5G,UAAW,GACX6G,mBAAoB,GACpBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBC,iBAAkB,CAAC,EAEnB5I,WAAY,GACZC,aAAc,GAEdM,eAAe,GACfsI,WAAW,IC/Eb,GACE9H,GAAI,GACJuE,MAAO,GACP7F,MAAO,GACP8B,UAAW,UACX0B,SAAU,KACV/C,UAAW,GACXqF,WAAY,GACZ/E,WAAY,GACZX,WAAY,GACZC,YAAa,GACbC,UAAW,GAEX+I,wBAAyB,GACzBC,kBAAmB,GACnBC,yBAA0B,GAC1BC,kCAAmC,GACnCC,yBAA0B,GAC1BC,gBAAiB,GACjBC,gBAAiB,GACjBC,+BAAgC,GAEhCtC,iBAAkB,GAElBY,aAAc,GACdI,mBAAoB,GACpBF,WAAY,GACZyB,gBAAiB,GACjBC,eAAgB,GAChBC,gBAAiB,GACjBC,eAAgB,GAChBjC,aAAc,GACdQ,gBAAiB,GAEjBI,WAAY,IAAI5N,KAChB6N,oBAAqB,IACrBhH,SAAU,GACVkH,YAAa,GACb5G,UAAW,GACX6G,mBAAoB,GACpBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBe,WAAY,GACZpB,eAAgB,GAChBM,iBAAkB,CAAC,EAEnB5I,WAAY,GACZC,aAAc,GAEdM,eAAe,GACfsI,WAAW,ICnDb,GACE9H,GAAI,GACJuE,MAAO,GACPqE,iBAAiB,GACjBlK,MAAO,GACP8B,UAAW,GACX0B,SAAU,KACVsC,WAAY,GACZC,WAAY,EACZtF,UAAW,GACXL,WAAY,GACZC,YAAa,GAEbK,QAAS,GACTsF,qBAAsB,GACtBrF,UAAW,GACXsF,cAAe,GACfC,UAAW,GACXC,YAAa,GACbC,aAAc,GACdC,aAAc,GACdC,cAAe,GACfC,UAAW,GACXC,aAAc,GACdC,gBAAiB,GACjBC,kBAAmB,GAEnBpG,UAAW,GACXuG,qBAAsB,GACtBC,gBAAiB,GACjBC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,mBAAoB,GACpBC,yBAA0B,GAC1BC,qBAAsB,GACtBC,oBAAqB,GAErBC,iBAAkB,GAClBC,oBAAqB,GACrBC,qBAAsB,GACtBC,sBAAuB,GACvBC,aAAc,GAEdK,aAAc,GACdF,qBAAqB,GAErBc,WAAY,IAAI5N,KAChB6N,oBAAqB,IACrBhH,SAAU,GACViH,eAAgB,GAChBC,YAAa,GACb5G,UAAW,GACX6G,mBAAoB,GACpBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBC,iBAAkB,CAAC,EAEnB5I,WAAY,GACZC,aAAc,GACd2J,QAAQ,CAAC,EACTnJ,UAAU,CAAC,EACXG,cAAc,CAAC,GC9DjB,GACEG,GAAI,GACJtB,MAAO,GACPC,SAAU,YACVC,cAAe,WACfC,gBAAiB,aACjBC,WAAY,GACZC,YAAa,GACbK,QAAS,GACTC,UAAW,GACXL,UAAW,GACX8J,aAAc,oBACdxJ,aAAc,iBACdG,WAAY,sBACZF,iBAAkB,sBAClB6G,aAAc,GACdxG,YAAa,GACbF,UAAW,CAAC,EACZC,cAAe,CAAC,EAChBE,cAAe,CAAC,EAGhB3F,OAAQ,GACRyG,OAAQ,GACRzJ,SAAU,GACVG,aAAc,GACduJ,UAAW,GAGX3B,WAAY,GACZC,aAAc,GAGdM,eAAe,ICjCjB,GACEQ,GAAI,GACJtB,MAAO,GACPC,SAAU,YACVC,cAAe,WACfC,gBAAiB,aACjBC,WAAY,GACZC,YAAa,GACbe,YAAa,kBACbd,UAAW,GACXe,cAAe,CAAC,EAChBF,cAAe,CAAC,EAEhB3F,OAAQ,GACRyG,OAAQ,GACRzJ,SAAU,GACVG,aAAc,GACduJ,UAAW,GAGX3B,WAAY,GACZC,aAAc,GAGdM,eAAe,ICxBjB,GACEQ,GAAI,GACJtB,MAAO,GACPC,SAAU,YACVC,cAAe,WACfC,gBAAiB,aACjBC,WAAY,GACZC,YAAa,GACbK,QAAS,GACTC,UAAW,GACXL,UAAW,GACX8J,aAAc,oBACdxJ,aAAc,iBACdG,WAAY,sBACZF,iBAAkB,sBAClB6G,aAAc,GACdxG,YAAa,GACbF,UAAW,CAAC,EACZC,cAAe,CAAC,EAChBE,cAAe,CAAC,EAGhB3F,OAAQ,GACRyG,OAAQ,GACRzJ,SAAU,GACVG,aAAc,GACduJ,UAAW,GAGX3B,WAAY,GACZC,aAAc,I,SC/BhB,GAAgB6J,EAAQC,KACtBD,EAAOH,iBAAiBI,EAAOJ,iBAC/BG,EAAOnI,UAAYoI,EAAOnB,iBAAiBjH,UACxCoI,EAAOnJ,gBACRmJ,EAAOnJ,cAAcoJ,KAAKC,MAAMF,EAAOnJ,gBAGzC,IAAK,IAAI5J,KAAQ8S,EACf,GAAa,cAAT9S,EACF8S,EAAOnI,UAAYoI,EAAOnB,iBAAiBjH,eACvC,GAAa,cAAT3K,EACR8S,EAAOvI,UAAYwI,EAAOrK,UAAY,kBACjC,GAAa,aAAT1I,EACT8S,EAAO7G,SAAW8G,EAAOpK,mBACpB,GAAa,eAAT3I,EACT8S,EAAOvE,WAAawE,EAAOnK,qBACvB,GAAI,UAAU1H,KAAKlB,GACvB8S,EAAO9S,GAAM,GACV+S,EAAOnJ,gBACRkJ,EAAO9S,GAAM+S,EAAOnJ,cAAc5J,QAE/B,IAAI,CAAC,aAAc,iBAAiB,aAAakN,QAAQlN,IAAS,EACvE,SAGA8S,EAAO9S,GAAQ+S,EAAO/S,EACvB,CAQH,OANI8S,EAAOtC,eACTsC,EAAOtC,aAAa,IAEtBsC,EAAOF,QAAQG,EACfD,EAAOF,QAAQnJ,UAAYuJ,KAAKC,MAAMF,EAAOtJ,WAC7CqJ,EAAOF,QAAQjJ,YAAcqJ,KAAKC,MAAMF,EAAOpJ,aACxCmJ,CAlCT,ECAA,aAAgC,IAAhBC,EAAgB,uDAAP,GACvB,OAAOA,EAAO1O,KAAIX,IAAQ,CACxB1D,KAAM0D,EAAK1D,KACXkT,MAAOxP,EAAKwP,MACZC,SAAUzP,EAAKyP,SACfC,WAAY1P,EAAK0P,WACjBC,YAAa3P,EAAK2P,YAClBC,UAAW5P,EAAK4P,UAChBC,UAAW7P,EAAK6P,aARpB,ECEA,GAAgBT,EAAQC,KACtB,IAAI/S,EAAO,GAQX,IAAKA,KADL8S,EAAOlJ,cAAc,CAAC,EACTkJ,EACPA,EAAO9S,KAAS+S,EAClBD,EAAO9S,GAAQ+S,EAAOD,EAAO9S,IACpB+S,EAAO/S,KAChB8S,EAAO9S,GAAQ+S,EAAO/S,IAK1B,IAAKA,KAAQ+S,EACE,OAAT/S,EAEE+S,EAAOhJ,GACT+I,EAAO/I,GAAKgJ,EAAOhJ,UAEZ+I,EAAO/I,GAER,YAAY7I,KAAKlB,GAEzB8S,EAAOrJ,UAAUzJ,GAAQ+S,EAAO/S,GACvB,gBAAgBkB,KAAKlB,IAGrB,YAAYkB,KAAKlB,GAD1B8S,EAAOpJ,cAAc1J,GAAQ+S,EAAO/S,GAGlB,aAATA,EAET8S,EAAOnJ,YAAcoJ,EAAOS,SACnB,UAAUtS,KAAKlB,KACxB8S,EAAOlJ,cAAc5J,GAAQyT,EAAmBV,EAAO/S,KAK3D,OAFA8S,EAAOlJ,cAAgBoJ,KAAKU,UAAUZ,EAAOlJ,eAEtCkJ,CA3CT,ECFA,GAAgBA,EAAQC,KACtBA,EAAOtJ,UAAYuJ,KAAKC,MAAMF,EAAOtJ,WACrCsJ,EAAOrJ,cAAgBsJ,KAAKC,MAAMF,EAAOrJ,eACzCqJ,EAAOpJ,YAAcqJ,KAAKC,MAAMF,EAAOpJ,aACvCoJ,EAAOnJ,cAAgBoJ,KAAKC,MAAMF,EAAOnJ,eAEzC,IAAK,IAAI5J,KAAQ8S,EACf,GAAa,cAAT9S,EACF8S,EAAOnI,UAAYoI,EAAOnB,iBAAiBjH,eACtC,GAAa,cAAT3K,EACT8S,EAAO9S,GAAQ+S,EAAOrJ,cAAc1J,QAC/B,GAAa,eAATA,EACT8S,EAAO9S,GAAQ+S,EAAOrJ,cAAc1J,QAC/B,GAAa,cAATA,EACT8S,EAAOvI,UAAYwI,EAAOrK,UAAY,kBACjC,GAAa,aAAT1I,EACT8S,EAAO7G,SAAW8G,EAAOpK,mBACpB,GAAa,eAAT3I,EACT8S,EAAOvE,WAAawE,EAAOnK,qBACtB,GAAI,YAAY1H,KAAKlB,GAE1B8S,EAAO9S,GAAQ+S,EAAOtJ,UAAUzJ,QAC3B,GAEL,gBAAgBkB,KAAKlB,IACrB,YAAYkB,KAAKlB,GAEjB8S,EAAO9S,GAAQ+S,EAAOrJ,cAAc1J,QAC/B,GAAI,UAAUkB,KAAKlB,GACxB8S,EAAO9S,GAAQ+S,EAAOnJ,cAAc5J,IAAS,OACxC,IAAI,CAAC,aAAc,kBAAkBkN,QAAQlN,IAAS,EAC3D,SAGA8S,EAAO9S,GAAQ+S,EAAO/S,EACvB,CAEH,OAAO8S,CArCT,ECEA,GAAgBA,EAAQC,KACtB,IAAI/S,EAAO,GAOX,IAAKA,KAAQ8S,EACPA,EAAO9S,KAAS+S,EAClBD,EAAO9S,GAAQ+S,EAAOD,EAAO9S,IACpB+S,EAAO/S,KAChB8S,EAAO9S,GAAQ+S,EAAO/S,IAI1B,IAAKA,KAAQ+S,EACE,OAAT/S,EAEE+S,EAAOhJ,GACT+I,EAAO/I,GAAKgJ,EAAOhJ,UAEZ+I,EAAO/I,GAEE,cAAT/J,EACT8S,EAAOpJ,cAAc1J,GAAQ+S,EAAO/S,GAC3B,YAAYkB,KAAKlB,GAE1B8S,EAAOrJ,UAAUzJ,GAAQ+S,EAAO/S,GACvB,gBAAgBkB,KAAKlB,IAGrB,YAAYkB,KAAKlB,GAD1B8S,EAAOpJ,cAAc1J,GAAQ+S,EAAO/S,GAGlB,aAATA,EAET8S,EAAOnJ,YAAcoJ,EAAOS,SACnB,UAAUtS,KAAKlB,KACxB8S,EAAOlJ,cAAc5J,GAAQyT,EAAmBV,EAAO/S,KAS3D,OALA8S,EAAOrJ,UAAYuJ,KAAKU,UAAUZ,EAAOrJ,WACzCqJ,EAAOpJ,cAAgBsJ,KAAKU,UAAUZ,EAAOpJ,eAC7CoJ,EAAOnJ,YAAcqJ,KAAKU,UAAUZ,EAAOnJ,aAC3CmJ,EAAOlJ,cAAgBoJ,KAAKU,UAAUZ,EAAOlJ,eAEtCkJ,CA/CT,ECFA,GAAgBA,EAAQC,KACtBA,EAAOY,UAAYX,KAAKC,MAAMF,EAAOY,WAAa,MAClDZ,EAAOjJ,cAAgBkJ,KAAKC,MAAMF,EAAOjJ,eAAiB,MAC1DiJ,EAAOnJ,cAAgBoJ,KAAKC,MAAMF,EAAOnJ,eAAiB,MAE1D,IAAK,IAAI5J,KAAQ8S,EACF,cAAT9S,EACF8S,EAAOnI,UAAYoI,EAAOnB,iBAAiBjH,UACzB,cAAT3K,EACT8S,EAAO9S,GAAQ+S,EAAOjJ,cAAc9J,GAClB,cAATA,EACT8S,EAAOvI,UAAYwI,EAAOrK,UAAY,aACpB,aAAT1I,EACT8S,EAAO7G,SAAW8G,EAAOpK,cACP,eAAT3I,EACT8S,EAAOvE,WAAawE,EAAOnK,gBAE3B,gBAAgB1H,KAAKlB,IACrB,iBAAiBkB,KAAKlB,IACtB,YAAYkB,KAAKlB,IACjB,YAAYkB,KAAKlB,GAGjB8S,EAAO9S,GAAQ+S,EAAOjJ,cAAc9J,GAC3B,UAAUkB,KAAKlB,GACxB8S,EAAO9S,GAAQ+S,EAAOnJ,cAAc5J,IAAS,GAI7C8S,EAAO9S,GAAQ+S,EAAO/S,GAG1B,OAAO8S,CAhCT,ECEA,GAAgBA,EAAQC,KACtB,IAAI/S,EAAO,GAOX,IAAKA,KAJD+S,EAAOxD,iBAAmBwD,EAAOvD,iBACnCuD,EAAOtD,cAAgBmE,KAAKzM,MAAM4L,EAAOxD,iBAA2C,IAAxBwD,EAAOvD,gBAAwB,KAGhFsD,EACPA,EAAO9S,KAAS+S,EAClBD,EAAO9S,GAAQ+S,EAAOD,EAAO9S,IACpB+S,EAAO/S,KAChB8S,EAAO9S,GAAQ+S,EAAO/S,IAI1B,IAAKA,KAAQ+S,EACE,OAAT/S,EAEE+S,EAAOhJ,GACT+I,EAAO/I,GAAKgJ,EAAOhJ,UAEZ+I,EAAO/I,GAEE,cAAT/J,EACT8S,EAAOhJ,cAAc9J,GAAQ+S,EAAO/S,GAC3B,iBAAiBkB,KAAKlB,IAAS,YAAYkB,KAAKlB,QAEpC6T,IAAjBd,EAAO/S,IAAwC,KAAjB+S,EAAO/S,KACvC8S,EAAOhJ,cAAc9J,GAAQ+S,EAAO/S,IAE7B,UAAUkB,KAAKlB,KACxB8S,EAAOlJ,cAAc5J,GAAQyT,EAAmBV,EAAO/S,KAO3D,OAHA8S,EAAOhJ,cAAgBkJ,KAAKU,UAAUZ,EAAOhJ,eAC7CgJ,EAAOlJ,cAAgBoJ,KAAKU,UAAUZ,EAAOlJ,eAEtCkJ,CAvCT,E,iBCeA,MAAMnQ,EAAQ,CACZmR,QAASnQ,OAAOC,OAAO,CAAC,EAAGmQ,GAC3BC,QAASrQ,OAAOC,OAAO,CAAC,EAAGqQ,GAC3BC,GAAIvQ,OAAOC,OAAO,CAAC,EAAGuQ,IAGlBtR,EAAU,CACduR,UAAUzR,EAAOE,GACf,OAAOF,EAAME,EAAQzB,aACtB,EACDiT,aAAa1R,EAAOE,GAClB,OAAQS,IACNA,EAAU4B,KAAKC,IAAI7B,EAAS,GAAK,EAEjC,IAAI0B,EAASrC,EAAME,EAAQzB,cAAciQ,oBACrCpM,EAAM,EACNb,GAAS,EAcb,OAbId,EAAU,GACR0B,GAAU,YACZA,EAASI,SAASJ,EAAS,YAC3B1B,GAAoB,GACpB2B,EAAMD,IAAY1B,EAAU,GAE5B2B,EAAM,EAGRA,EAAMD,IAAY1B,EAAU,EAG9Bc,KAAYd,IAAY+B,MAAM/B,EAAU,IAAe,EAAN2B,GAC1Cb,CAAP,CAEH,GAGGhB,EAAY,CAChBkR,iBAAiB3R,EAAOW,GACO,YAAzBA,EAAQlC,aACVuB,EAAMmR,QAAUtM,EAAE+M,MAAMR,GACS,OAAzBzQ,EAAQlC,aAChBuB,EAAMuR,GAAK1M,EAAE+M,MAAMJ,GAEnBxR,EAAMqR,QAAUxM,EAAE+M,MAAMN,EAE3B,EACDO,eAAe7R,EAAOW,GACpBX,EAAMmR,QAAQ3K,QAAU7F,EAAQyG,IAAM,GACtCpH,EAAMmR,QAAQ1K,UAAY9F,EAAQwK,cAAgB,GAClDnL,EAAMmR,QAAQjF,aAAevL,EAAQmR,iBAAmB,GACxD9R,EAAMmR,QAAQ/E,cAAgBzL,EAAQoR,eAAiB,GACvD/R,EAAMmR,QAAQhF,aAAexL,EAAQqR,kBAAoB,GACzDhS,EAAMmR,QAAQ5E,gBAAkB5L,EAAQyG,GACxCpH,EAAMmR,QAAQpF,cAAgBpL,EAAQsR,cAAgB,GACtDjS,EAAMmR,QAAQnF,UAAYrL,EAAQuR,UAAY,GAC9ClS,EAAMmR,QAAQ9E,UAAY1L,EAAQkJ,MAAQ,GAC1C7J,EAAMmR,QAAQ7E,aAAe3L,EAAQwR,UAAY,GACjDnS,EAAMmR,QAAQlF,YAActL,EAAQyR,WACpCpS,EAAMmR,QAAQ3E,kBAAoB7L,EAAQuP,YAC3C,EAACmC,kBAAkBrS,EAAOW,GACzBX,EAAMuR,GAAG/K,QAAU7F,EAAQyG,IAAM,GACjCpH,EAAMuR,GAAG9K,UAAY9F,EAAQwK,cAAgB,GAC7CnL,EAAMuR,GAAGrF,aAAevL,EAAQmR,iBAAmB,GACnD9R,EAAMuR,GAAGnF,cAAgBzL,EAAQoR,eAAiB,GAClD/R,EAAMuR,GAAGpF,aAAexL,EAAQqR,kBAAoB,GACpDhS,EAAMuR,GAAGhF,gBAAkB5L,EAAQyG,GACnCpH,EAAMuR,GAAGxF,cAAgBpL,EAAQsR,cAAgB,GACjDjS,EAAMuR,GAAGvF,UAAYrL,EAAQuR,UAAY,GACzClS,EAAMuR,GAAGlF,UAAY1L,EAAQkJ,MAAQ,GACrC7J,EAAMuR,GAAGjF,aAAe3L,EAAQwR,UAAY,GAC5CnS,EAAMuR,GAAGtF,YAActL,EAAQyR,WAC/BpS,EAAMuR,GAAG/E,kBAAoB7L,EAAQuP,YACtC,GAGGhP,EAAU,CACRoG,iBAAN,EAA8C3G,GAAS,+BAA9BT,EAA8B,EAA9BA,QAASoS,EAAqB,EAArBA,SAAqB,cACzBC,EAAAA,EAAAA,iBAA8B,CACxDnL,GAAIzG,EAAQyG,GACZM,SAAU/G,EAAQ+G,SAClBpJ,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,eAL6B,eAC9C2C,EAD8C,KACtCI,EADsC,KAoBrD,OAbIJ,IAC2B,YAAzBlB,EAAQzB,cACV+T,EAAetS,EAAQuR,UAAW5M,EAAE4N,MAAMjR,EAAIC,OAAOoE,KAAMrE,EAAIC,OAAOiR,cAEtEJ,EAAS,eAAgBjC,KAAKC,MAAM9O,EAAIC,OAAOoE,KAAKmB,eACnB,OAAzB9G,EAAQzB,cAChBkU,EAAUzS,EAAQuR,UAAW5M,EAAE4N,MAAMjR,EAAIC,OAAOoE,KAAMrE,EAAIC,OAAOiR,cAEjEJ,EAAS,iBAAkBjC,KAAKC,MAAM9O,EAAIC,OAAOoE,KAAKmB,eAEtD4L,EAAe1S,EAAQuR,UAAW5M,EAAE4N,MAAMjR,EAAIC,OAAOoE,KAAMrE,EAAIC,OAAOiR,eAGnE,CAACtR,EAAQI,EApBqC,KAqBtD,EACKqG,cAAN,GAAiC,+BAAX3H,EAAW,EAAXA,QAChBkQ,EAASvL,EAAE4N,MACb,CACEnU,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,cAExByB,EAAQuR,WAGmB,YAAzBvR,EAAQzB,cAEuB,OAAzByB,EAAQzB,aADhB2R,EAAOS,SAAWhM,EAAE+M,MAAM1R,EAAQ2Q,UAIlCT,EAAOyC,MAAQhO,EAAE+M,MAAM1R,EAAQ2S,OAGjC,MAAMtR,EACqB,YAAzBrB,EAAQzB,aACJqU,EAAiBjO,EAAE+M,MAAMmB,GAAiB3C,GACjB,OAAzBlQ,EAAQzB,aAAwBuU,EAAYnO,EAAE+M,MAAMqB,GAAY7C,GAC9D8C,EAAiBrO,EAAE+M,MAAMuB,GAAiB/C,GArBnB,QAuBHmC,EAAAA,EAAAA,cAA2BhR,GAvBxB,eAuBxBH,EAvBwB,KAuBhBI,EAvBgB,KA2B/B,OAHIJ,IACFlB,EAAQuR,UAAUrK,GAAK5F,EAAIC,OAAO2F,IAE7B,CAAChG,EAAQI,EA3Be,KA4BhC,EACKsG,mBAAN,EAAsCnH,GAAS,+BAApBT,EAAoB,EAApBA,QACrBkQ,EAASvL,EAAE4N,MACb,CACEnU,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,cAExBoG,EAAE4N,MAAMvS,EAAQuR,UAAW9Q,IAGA,YAAzBT,EAAQzB,aACV2R,EAAOS,SAAWhM,EAAE+M,MAAM1R,EAAQ2Q,UACD,OAAzB3Q,EAAQzB,aAChB2R,EAAOS,SAAWhM,EAAE+M,MAAM1R,EAAQkT,YAElChD,EAAOyC,MAAQhO,EAAE+M,MAAM1R,EAAQ2S,OAGjC,MAAMtR,EACqB,YAAzBrB,EAAQzB,aACJqU,EAAiBjO,EAAE+M,MAAMmB,GAAiB3C,GACjB,OAAzBlQ,EAAQzB,aACNuU,EAAYnO,EAAE+M,MAAMqB,GAAY7C,GACjC8C,EAAiBrO,EAAE+M,MAAMuB,GAAiB/C,GAtBJ,QAuBjBmC,EAAAA,EAAAA,mBAAgChR,GAvBf,eAuBtCH,EAvBsC,KAuB9BI,EAvB8B,KAwB7C,MAAO,CAACJ,EAAQI,EAxB6B,KAyB9C,EACKyG,eAAN,EAA6CtH,GAAS,+BAA/BT,EAA+B,EAA/BA,QAASmT,EAAsB,EAAtBA,UAC1B9R,EAASsD,EAAE4N,MACb,CACEnU,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,cAExBkC,GANkD,cASxB4R,EAAAA,EAAAA,eAA4BhR,GATJ,eAS7CH,EAT6C,KASrCI,EATqC,KAiBpD,OAPIJ,GACFiS,EAAUC,KAAKC,SAAS/S,KAAKH,MAAK,CAACU,EAAMyS,KACnCzS,EAAKqG,KAAOzG,EAAQyG,IACtB/C,EAAAA,WAAAA,IAAQgP,EAAUC,KAAKC,SAAS/S,KAAKgT,GAAQ,aAAc,EAC5D,IAGE,CAACpS,EAAQI,EAjBoC,KAkBrD,EACKiS,YAAN,EAA+B9S,GAAS,+BAApBT,EAAoB,EAApBA,QACdqB,EAASsD,EAAE4N,MACb,CAAEnU,SAAU4B,EAAQ5B,SAAUG,aAAcyB,EAAQzB,cACpDkC,GAHoC,cAMV4R,EAAAA,EAAAA,aAA0BhR,GANhB,eAM/BH,EAN+B,KAMvBI,EANuB,KAOtC,MAAO,CAACJ,EAAQI,EAPsB,KAQvC,EACKkS,gBAAN,EAAmC/S,GAAS,+BAApBT,EAAoB,EAApBA,QAClBqB,EAASsD,EAAE4N,MACb,CAAEnU,SAAU4B,EAAQ5B,SAAUG,aAAcyB,EAAQzB,cACpDkC,GAHwC,cAMd4R,EAAAA,EAAAA,cAA2BhR,GANb,eAMnCH,EANmC,KAM3BI,EAN2B,KAO1C,MAAO,CAACJ,EAAQI,EAP0B,KAQ3C,EACK6G,gBAAN,EAA8C1H,GAAS,+BAA/BT,EAA+B,EAA/BA,QAASmT,EAAsB,EAAtBA,UAC3B9R,EAASsD,EAAE4N,MACb,CACEnU,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,cAExBkC,GANmD,cASzB4R,EAAAA,EAAAA,gBAA6BhR,GATJ,eAS9CH,EAT8C,KAStCI,EATsC,KAarD,OAHIJ,IACFiS,EAAUC,KAAKK,KAAKnT,KAAO6S,EAAUC,KAAKK,KAAKnT,KAAKoT,QAAQ7S,GAASA,EAAKqG,KAAOzG,EAAQyG,MAEpF,CAAChG,EAAQI,EAbqC,KActD,EAEKoI,UAAN,EAAoBjJ,GAAS,oDACC4R,EAAAA,EAAAA,UAAuB5R,GADxB,eACpBS,EADoB,KACZI,EADY,KAE3B,MAAO,CAACJ,EAAQI,EAFW,KAG5B,EACDuI,YAAY,EAAapJ,GAAS,IAApBT,EAAoB,EAApBA,QACZ,MAAMqB,EAASsD,EAAE4N,MACf,CACEzI,YAAsC,YAAzB9J,EAAQzB,aAA6B,mBAAqB,oBAEzEkC,GAGF4R,EAAAA,EAAAA,YAAyBhR,EAC1B,EACK8I,cAAN,EAAiC1J,GAAS,+BAApBT,EAAoB,EAApBA,QACpB,MAAMqB,EAASsD,EAAE4N,MACf,CACE3M,MAAO5F,EAAQuR,UAAU3L,MACzBwD,SAAUpJ,EAAQuR,UAAUnI,SAC5B1B,UAAW1H,EAAQuR,UAAU7J,UAC7BnJ,aAAcyB,EAAQzB,aACtB2I,GAAIlH,EAAQuR,UAAUrK,IAExBzG,GATsC,QAYZ4R,EAAAA,EAAAA,cAA2BhR,GAZf,eAYjCH,EAZiC,KAYzBI,EAZyB,KAaxC,MAAO,CAACJ,EAAQI,EAbwB,KAczC,EACKoH,cAAN,GAAiC,+BAAX1I,EAAW,EAAXA,QAAW,cACHqS,EAAAA,EAAAA,cAA2BrS,EAAQuR,WADhC,eACxBrQ,EADwB,KAChBI,EADgB,KAM/B,OAJIJ,IACFlB,EAAQuR,UAAUvL,WAAa1E,EAAIC,OAAOjB,KAAK0F,WAC/ChG,EAAQuR,UAAUtL,YAAc3E,EAAIC,OAAOjB,KAAK2F,aAE3C,CAAC/E,EAAQI,EANe,KAOhC,GAGH,OACExB,QACAS,YACAS,UACAhB,U,iCCzQF,MAAMF,EAAQ,CACZ6T,UAAW,IAGP3T,EAAU,CACdkT,WAAWpT,GACT,OAAOA,EAAM6T,SACd,GAGGpT,EAAY,CAAC,EAEbS,EAAU,CAEd4S,eAAe,EAA8BnT,GAAS,IAArCX,EAAqC,EAArCA,MAAqC,EAA9BE,QAA8B,EAArBoS,SAC/BtS,EAAM6T,UAAUlT,CACjB,GAGH,QACEX,QACAS,YACAS,UACAhB,U,sECrBF,MAAMF,EAAQ,CACZoF,MAAO,CACLlG,MAAO,OACP6U,MAAM,EACNxS,OAAQ,CAAC,EACT+R,KAAM,CACJU,MAAO,EACPC,SAAS,EACTC,YAAa,SACb1T,KAAM,GACN2I,KAAM,EACN8B,SAAU,KAGdkJ,eAAgB,CACdjV,MAAO,SACP6U,MAAM,EACNxS,OAAQ,CAAC,GAEX6S,eAAgB,CACdlV,MAAO,WACP6U,MAAM,EACNxS,OAAQ,CAAC,IAIPrB,EAAU,CAAC,EAEXO,EAAY,CAChB4T,YAAYrU,EAAOW,GACjB,IAAItD,EAAOsD,EAAQ2T,WAEnB,IAAKjX,EAAM,OAAO,EAElB2C,EAAM3C,GAAM0W,MAAO,EACnB/T,EAAM3C,GAAM6B,MAAQyB,EAAQzB,OAASc,EAAM3C,GAAM6B,MACjDc,EAAM3C,GAAMkE,OAASZ,EAAQY,OAEzBvB,EAAM3C,GAAMiW,OACdtT,EAAM3C,GAAMiW,KAAKnK,KAAO,EACxBnJ,EAAM3C,GAAMiW,KAAKU,MAAQ,EAE5B,EACDO,iBAAiBvU,GACfA,EAAMoF,MAAMkO,KAAKU,MAAQ,EACzBhU,EAAMoF,MAAMkO,KAAKnK,KAAO,CACzB,EACDqL,YAAYxU,EAAOW,GACjB,IAAItD,EAAOsD,EAAQ2T,WAEnB,IAAKjX,EAAM,OAAO,EAElB2C,EAAM3C,GAAM0W,MAAO,CACpB,GAGG7S,EAAU,CACR8J,mBAAN,EAA6CrK,GAAS,+BAA3BX,EAA2B,EAA3BA,MAAOE,EAAoB,EAApBA,QAC5BqB,EAAS,CAAC,EAEd,GAAIvB,EAAMoF,MAAMkO,KAAKW,QAAS,OAAO,EAErCjU,EAAMoF,MAAMkO,KAAK9S,KAAO,GAExBe,EAASsD,EAAE4N,MACT,CACEtJ,KAAMnJ,EAAMoF,MAAMkO,KAAKnK,KACvB8B,SAAUjL,EAAMoF,MAAMkO,KAAKrI,SAC3BrD,UAAW1H,EAAQuR,UAAU7J,UAC7ByD,QAAS,QACT5M,aAAcyB,EAAQzB,aACtB8H,UAAWrG,EAAQuR,UAAUpL,YAAcnG,EAAQuR,UAAUlL,UAC7DT,MAAO5F,EAAQuR,UAAU3L,OAE3BnF,GAGFX,EAAMoF,MAAMkO,KAAKW,SAAU,EApByB,cAqBxBQ,EAAAA,EAAAA,mBAAiClT,GArBT,eAqB7CH,EArB6C,KAqBrCI,EArBqC,KA4BpD,OANAxB,EAAMoF,MAAMkO,KAAKW,SAAU,EAEvB7S,IACFpB,EAAMoF,MAAMkO,KAAK9S,KAAOgB,EAAIkT,UAC5B1U,EAAMoF,MAAMkO,KAAKU,MAAQxS,EAAIwS,OAExB,CAAC5S,EAAQI,EA5BoC,KA6BrD,GAGH,QACExB,QACAS,YACAS,UACAhB,U,4CC5FF,MAAMF,EAAQ,CACZvB,aAAc,GACdH,SAAU,GACVqW,aAAa,GAGTzU,EAAU,CACdzB,aAAauB,GACX,OAAOA,EAAMvB,YACd,EACDH,SAAS0B,GACP,OAAOA,EAAM1B,QACd,EACDqW,YAAY3U,GACV,OAAOA,EAAM2U,WACd,GAGGlU,EAAY,CAChBmU,WAAW5U,EAAOW,GAChB,IAAK,IAAItD,KAAQsD,EACfX,EAAM3C,GAAQsD,EAAQtD,EAEzB,GAGG6D,EAAU,CAAC,EAEjB,QACElB,QACAS,YACAS,UACAhB,U,qECjCF,MAAMF,EAAQ,CACZ6U,KAAM,CACJC,aAAc,CACZhP,MAAO,GACPyD,OAAQ,GACR9C,UAAW,GACX6C,SAAU,GACV1B,UAAW,GACXmB,UAAW,IAEbgM,WAAY,CACV9L,MAAO,GACPE,KAAM,EACN6K,MAAO,GAET5S,OAAQ,CACN6S,SAAS,GAEXzT,KAAM,IAER+S,SAAU,CACRuB,aAAc,CACZhP,MAAO,GACPyD,OAAQ,GACR9C,UAAW,GACX6C,SAAU,GACV1B,UAAW,GACXmB,UAAW,IAEbgM,WAAY,CACV9L,MAAO,GACPE,KAAM,EACN6K,MAAO,GAET5S,OAAQ,CACN6S,SAAS,GAEXzT,KAAM,IAERwU,iBAAkB,GAGd9U,EAAU,CACd+U,cAAcjV,GACZ,OAAQW,MAEJA,IACC+B,MAAM/B,EAAU,IACbX,EAAMgV,kBAAqBrU,EAAU,EAAM,EAGpD,GAGGF,EAAY,CAAC,EAEbS,EAAU,CACRgU,cAAN,GAAwC,+BAAlBlV,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,QAC3B,GAAIF,EAAM6U,KAAKzT,OAAO6S,QAAS,OAAO,EACtC,MAAM1S,EAASsD,EAAE4N,MACf,CACEnR,OAAQ,WACRhD,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,cAExBoG,EAAE4N,MAAMzS,EAAM6U,KAAKC,aAAc9U,EAAM6U,KAAKE,aAG9C/U,EAAM6U,KAAKzT,OAAO6S,SAAU,EAXU,cAYV1B,EAAAA,EAAAA,aAA0BhR,GAZhB,eAY/BH,EAZ+B,KAYvBI,EAZuB,KAmBtC,OANAxB,EAAM6U,KAAKzT,OAAO6S,SAAU,EAExB7S,IACFpB,EAAM6U,KAAKrU,KAAOgB,EAAIkT,UACtB1U,EAAM6U,KAAKE,WAAWf,MAAQxS,EAAIwS,OAE7B,CAAC5S,EAAQI,EAnBsB,KAoBvC,EACK2T,kBAAN,GAA4C,+BAAlBnV,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,QAC/B,GAAIF,EAAMuT,SAASnS,OAAO6S,QAAS,OAAO,EAC1C,MAAM1S,EAASsD,EAAE4N,MACf,CACEnR,OAAQ,UACRhD,SAAU4B,EAAQ5B,SAClBG,aAAcyB,EAAQzB,cAExBoG,EAAE4N,MAAMzS,EAAMuT,SAASuB,aAAc9U,EAAMuT,SAASwB,aAGtD/U,EAAMuT,SAASnS,OAAO6S,SAAU,EAXU,cAYd1B,EAAAA,EAAAA,aAA0BhR,GAZZ,eAYnCH,EAZmC,KAY3BI,EAZ2B,KAmB1C,OANAxB,EAAMuT,SAASnS,OAAO6S,SAAU,EAE5B7S,IACFpB,EAAMuT,SAAS/S,KAAOgB,EAAIkT,UAC1B1U,EAAMuT,SAASwB,WAAWf,MAAQxS,EAAIwS,OAEjC,CAAC5S,EAAQI,EAnB0B,KAoB3C,EACK8G,kBAAN,GAA4C,+BAAlBtI,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,QAC/B,MAAMqB,EAAS,CACbjD,SAAU4B,EAAQ5B,SAClBiK,YAAwC,YAAzBrI,EAAQzB,aAA6B,UAAqC,OAAzByB,EAAQzB,aAAuB,KAAK,WAAvF,UAH2B,QAKd8T,EAAAA,EAAAA,kBAA+BhR,GALjB,eAKnCH,EALmC,KAK3BI,EAL2B,KAS1C,OAHIJ,IACFpB,EAAMgV,iBAAmBxT,EAAIC,OAAOY,QAE/B,CAACjB,EAAQI,EAT0B,KAU3C,GAGH,QACExB,QACAS,YACAS,UACAhB,U,sGCtHF,GACE,CACEkV,aAAc,MACdC,cAAe,GACfC,WAAY,IAEd,CACEF,aAAc,MACdC,cAAe,GACfC,WAAY,IAEd,CACEF,aAAc,KACdC,cAAe,GACfC,WAAY,IAEd,CACEC,SAAU,QACVH,aAAc,KACdC,cAAe,GACfC,WAAY,KCpBhB,GACE,CACEF,aAAc,UACdC,cAAe,GACfC,WAAY,IAEd,CACEF,aAAc,KACdC,cAAe,GACfC,WAAY,IAEd,CACEC,SAAU,QACVH,aAAc,KACdC,cAAe,GACfC,WAAY,K,SCVhB,MAAMtV,EAAQ,CACZ6T,UAAW,IAGP3T,EAAU,CACd2Q,SAAS7Q,GACP,OAAOA,EAAM6T,SACd,GAGGpT,EAAY,CAAC,EAEbS,EAAU,CACdsU,aAAa,EAA8B7U,GAAS,IAArCX,EAAqC,EAArCA,MAAOE,EAA8B,EAA9BA,QAASoS,EAAqB,EAArBA,SACzB3R,EACFA,EAAQe,KAAI,CAACX,EAAMyS,KACjBnP,EAAAA,WAAAA,IAAQrE,EAAM6T,UAAWL,EAAOzS,EAAhC,IAGE,CAAC,KAAKwJ,QAAQ,GAAKrK,EAAQuR,UAAU3L,QAAU,EAC5C9F,EAAM6T,UAAU,IAA0C,QAApC7T,EAAM6T,UAAU,GAAGuB,eAC5CpV,EAAM6T,UAAYhP,EAAE+M,MAAM6D,IAEvBzV,EAAM6T,UAAU,IAA0C,cAApC7T,EAAM6T,UAAU,GAAGuB,eAC5CpV,EAAM6T,UAAYhP,EAAE+M,MAAM8D,IAGhCpD,EAAS,mBACV,EACK3H,iBAAN,GAA2C,+BAAlB3K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,QAAW,cACbuU,EAAAA,EAAAA,iBAA+BvU,EAAQuR,WAD1B,eAClCrQ,EADkC,KAC1BI,EAD0B,KAEzC,GAAIJ,EAAQ,CACV,IAAIuU,EAAkB,EACtB3V,EAAM6T,UAAUnS,KAAKkU,IACnBpU,EAAIC,OAAOjB,KAAKkB,KAAKmU,IACfD,EAAUR,eAAiBS,EAASC,kBACtCF,EAAUN,WAAaO,EAASE,qBAChCJ,EAAkBpT,KACfyT,IAAIzT,KAAK0T,UAAUN,GAAkBpT,KAAK0T,UAAUL,EAAUN,aAC9DY,UACJ,IAE4B,OAA3BN,EAAUR,eACZQ,EAAUN,WAAaK,EACxB,GAEJ,CACD,MAAO,CAACvU,EAAQI,EAlByB,KAmB1C,GAGH,OACExB,QACAS,YACAS,UACAhB,U,uBC5DF/C,EAAOC,QAAU,CACf,CACEoB,KAAM,sBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,6BAGX,CACEV,KAAM,sBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAC,QAAF,4CAC/B3F,KAAM,CACJD,MAAO,6BAGX,CACEV,KAAM,gBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,6BAGX,CACEV,KAAM,gBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,IAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,6BAGX,CACEV,KAAM,oBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,IAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,sCAGX,CACEV,KAAM,oBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,sCAIX,CACEV,KAAM,eACNI,UAAY4G,GAAYV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,iCAGX,CACEV,KAAM,iBACNI,UAAY4G,GAAYV,EAAAA,EAAAA,KAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,wBAGX,CACEV,KAAM,WACNI,UAAY4G,GAAYV,EAAAA,EAAAA,IAAAA,KAAAA,WAAQ,OAAC,SAAF,4CAC/B3F,KAAM,CACJD,MAAO,wB,uBC9Db/B,EAAOC,QAAU,CACf+Y,MAAOrR,EAAAA,MAAAA,EACPwO,KAAMxO,EAAAA,MAAAA,EACNsR,OAAQtR,EAAAA,MAAAA,EACRxH,OAAQwH,EAAAA,MAAAA,EACR+L,SAAU/L,EAAAA,MAAAA,EACVsO,WAAYtO,EAAAA,KAAAA,E,2ICJP,MAEMuR,EAAO,0BAAG,YAOnB,OAAOC,MAAQA,IAAIC,SAAW,aAEjC,IATmB,qDAWPC,EAAkB,0BAAG,UAAOC,GACvC,MAAMjW,EAAOiW,EAAEjW,KACf,GAAKiW,EAAEjW,MAeA,GAAkB,cAAdA,EAAKkW,KACdC,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAASrW,EAAKsW,gBAEX,GAAItW,EAAKuW,OAA6B,IAApBvW,EAAKuW,MAAML,KAClCC,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAAS,gCAEN,GAAIrW,EAAKiB,QAA+B,YAArBjB,EAAKiB,OAAOiV,MACX,iBAArBlW,EAAKiB,OAAOiV,KACd,OAAOL,QA3BS,wCAAdI,EAAEI,QACJF,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAAS,qBAGXF,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAAS,2BAoBf,MAAO,EAAC,EACT,IAnC8B,sDAsClBG,EAAsB,0BAAG,UAAOP,GAE3C,OAAIA,EAAEjW,KAAK+J,QAAQ,sBAAwB,GAIvCkM,EAAEQ,QAAQC,YAAY3M,QAAQ,aAAe,EAHxC8L,IAMF,EAAC,EACT,IAVkC,sDChDjCc,IAAAA,SAAAA,iBAAiC,EAGnC,gCAAe,aAA6E,QAApE7V,OAAAA,OAAoE,MAA3D,MAA2D,EAApD9C,EAAoD,EAApDA,KAAoD,IAA9C+C,OAAAA,OAA8C,MAArC,KAAqC,MAA/Bf,KAAAA,OAA+B,MAAxB,KAAwB,EAAlBgH,EAAkB,EAAlBA,YACxE,IACM4P,EAIJ7V,EAASP,OAAOC,OAAO,CAAC,EAAGM,EAAQ,CAAE8V,EAAGpG,KAAKqG,WAC7C/V,EAAoB,QAAXD,EAAmBN,OAAOC,OAAO,CAAC,EAAGM,EAAQf,GAAQe,EAE9D,IAAIgW,EAAoB,kCAEtBA,EADkB,SAAhB/P,EACkB,kCACK,SAAhBA,EACW,mDAEAA,EAGlBhH,GAAwB,SAAhBgH,GAA0B,iBAAiBjJ,KAAK+C,KAC1Dd,EAAOgX,IAAAA,UAAahX,IAGtB,MAAMgB,QAAY2V,IAAM,CACtB7V,OAAQA,EACRmW,IAAK,IAAMjZ,EACXkZ,QACoF,KACpFnW,OAAQA,EACRoW,QAAS,CACP,eAAgBJ,EAChBK,OAAQ,OAEVpX,KAAM,iBAAiBjC,KAAK+C,GAAUd,EAAO,KAG/C,OAAIgB,EAAImW,QAAQ,gBAAgBpN,QAAQ,cAAgB,SAChDyM,EAAuBxV,GACtB,EAAC,EAAOA,EAAIhB,OAKlBgB,EAAIhB,MAAQgB,EAAIhB,KAAKkW,MAAQ,CAAC,UAAW,QAAQnM,QAAQ/I,EAAIhB,KAAKkW,MAAQ,GAC1ElV,EAAIhB,MAAQgB,EAAIhB,KAAKuW,OACrBvV,EAAIhB,MACHgB,EAAIhB,KAAKiB,QACTD,EAAIhB,KAAKiB,OAAOiV,MAChB,CAAC,UAAW,QAAQnM,QAAQ/I,EAAIhB,KAAKiB,OAAOiV,MAAQ,SAEhDF,EAAmBhV,GAClB,EAAC,EAAOA,EAAIhB,OAGd,EAAC,EAAMgB,EAAIhB,KAInB,CAHC,MAAOiW,GAEP,aADMD,EAAmBC,GAClB,EAAC,EAAOA,EAAEjW,KAClB,CACF,IA1DD,sDCNA,I,wCCGA,SAASqX,IAA8C,IAA/B5X,EAA+B,uDAArB,CAAC,EAAG6X,EAAiB,uDAAJ,CAAC,EAQlD,OAPAra,KAAKwW,QAAUvQ,EAAAA,QAAAA,QAAgB,CAACqU,MAAM,EAAKC,KAAM,aACjDva,KAAKwC,QAAU4E,EAAE+M,MAAM3R,GACvBxC,KAAKqa,WAAaA,EAClBra,KAAKwa,IAAM,GACXxa,KAAKya,SAAW,GAChBza,KAAKyM,SAAW,GAChBzM,KAAK0a,MACE,EAAC,EACT,CAEDN,EAAcO,UAAY,CAClBD,MAAO,4CACL,EAAKE,mBACL,EAAKC,cAFA,KAGZ,EACDC,cAAeP,GACbva,KAAKwW,QAAQ+D,KAAOA,CACrB,EACDQ,eACE/a,KAAKwW,QAAQwE,OACd,EACKJ,aAAc,oDACU9Q,EAAAA,EAAAA,GAAI,EAAKtH,SADnB,eACXmB,EADW,KACHI,EADG,KAMlB,OAJIJ,IACF,EAAK6W,IAAMzW,EAAIkX,eAAeT,IAC9B,EAAKM,cAAc/W,EAAIkX,eAAe7B,UAEjC,CAACzV,EAAQI,EANE,KAOnB,EACK8W,eAAgB,oDACQ/Q,EAAAA,EAAAA,GAAI,CAC9BjG,OAAQ,MACR9C,KAAM,4BACNgJ,YAAa,OACbhH,KAAM,CAACyX,IAAK,EAAKA,IAAKX,OAAQ/U,KAAK+U,YALjB,eACblW,EADa,KACLI,EADK,KAOpB,OAAIJ,GAAUI,EAAIkX,gBAAgD,YAA9BlX,EAAIkX,eAAetX,QACrD,EAAK8W,SAAW1W,EAAIkX,eAAe5a,MAAMoa,UAAY,EAAKJ,WAAWI,SACrE,EAAKhO,SAAW1I,EAAIkX,eAAe5a,MAAMoM,UAAY,EAAK4N,WAAW5N,SACrE,EAAKyO,eACE,EAAC,IACCvX,GAAUI,EAAIkX,gBAAgD,UAA9BlX,EAAIkX,eAAetX,QAC5D,EAAKoX,eACLI,EAAAA,aAAAA,MAAmB,CACjB1Z,MAAO,OACP2X,QAASrV,EAAIkX,eAAe7B,UAEvB,EAAC,KAEJrV,EAAIkX,gBAAkBlX,EAAIkX,eAAe7B,SAC3C,EAAK0B,cAAc/W,EAAIkX,eAAe7B,SAG1CgC,YAAW,KACT,EAAKP,cAAL,GACC,KACI,EAAC,GA3BY,KA4BrB,EACDK,gBACE7O,EAAAA,EAAAA,GAAS,CACPtL,KAAM,qBACNgC,KAAM,CACJ0X,SAAUza,KAAKya,SACfhO,SAAUzM,KAAKyM,SACf4O,YAAY,GAEd7Y,QAAS,CACPmQ,OAAQ,WAGZ3S,KAAK+a,cACN,GAGH,OAAgBvY,EAAS6X,IAChB,IAAID,EAAc5X,EAAS6X,GC7EpC,GACEiB,aAAa,GAAoB,IAAlBzX,EAAkB,EAAlBA,OAAQC,EAAU,EAAVA,OAGrB,OADAyX,QAAQC,KAAK,iBAAkB,CAAExR,QAAS,MAAOL,GAAI,EAAG9F,SAAQC,WACzDgG,EAAI,CACTjG,OAAQ,OACR9C,KAAM,iBACNgJ,YAAa,OACbhH,KAAM,CAAEiH,QAAS,MAAOL,GAAI,EAAG9F,SAAQC,WAE1C,EAED2X,YAAY,GAA+D,QAA7D5X,OAAAA,OAA6D,MAApD,OAAoD,EAA5C9C,EAA4C,EAA5CA,KAA4C,IAAtCgJ,YAAAA,OAAsC,MAAxB,OAAwB,EAAhBhH,EAAgB,EAAhBA,KAAMe,EAAU,EAAVA,OAG/D,OADAyX,QAAQC,KAAK,eAAgB,CAAE3X,SAAQ9C,OAAMgJ,cAAahH,OAAMe,WACzDgG,EAAI,CAAEjG,SAAQ9C,OAAMgJ,cAAahH,OAAMe,UAC/C,EAEDuI,SAAS,GAAiC,IAA/BtL,EAA+B,EAA/BA,KAAMgC,EAAyB,EAAzBA,KAAMe,EAAmB,EAAnBA,OAAQtB,EAAW,EAAXA,QAG7B,OADA+Y,QAAQC,KAAK,YAAa,CAAEza,OAAMgC,OAAMe,SAAQtB,aACzC6J,EAAAA,EAAAA,GAAS,CAAEtL,OAAMgC,OAAMe,SAAQtB,WACvC,EAED4X,cAAc,GAAgF,QAA9EvW,OAAAA,OAA8E,MAArE,OAAqE,EAA7D9C,EAA6D,EAA7DA,KAA6D,IAAvDgJ,YAAAA,OAAuD,MAAzC,OAAyC,EAAjChH,EAAiC,EAAjCA,KAAMe,EAA2B,EAA3BA,OAAUuW,EAAiB,uDAAJ,CAAC,EAGzF,OADAkB,QAAQC,KAAK,mBAAoB,CAAE3X,SAAQ9C,OAAMgJ,cAAahH,OAAMe,UAAUuW,GACvED,EAAc,CAAEvW,SAAQ9C,OAAMgJ,cAAahH,OAAMe,UAAUuW,EACnE,E,kDC9BH,QACEqB,QAASP,EAAAA,aAAAA,QACTK,KAAML,EAAAA,aAAAA,KACNQ,QAASR,EAAAA,aAAAA,QACT7B,MAAO6B,EAAAA,aAAAA,M,2ECHT,kBAAmE,6DAAP,CAAC,EAA3Cpa,EAAiD,EAAjDA,KAAiD,IAA3C+C,OAAAA,OAA2C,MAAlC,CAAC,EAAiC,EAA9Bf,EAA8B,EAA9BA,KAA8B,IAAxBP,QAAAA,OAAwB,MAAd,CAAC,EAAa,EAC7DgU,EAAU5P,EAAAA,WAAAA,SAAa,CACzB0T,MAAM,EACNC,KAAM,OACNqB,QAAS,kBACTC,WAAY,uBAGVzT,EAAOvG,SAASia,cAAc,QAKlChY,EAASiW,IAAAA,UAAajW,GAClBA,IAEA/C,EADE,MAAMD,KAAKC,GACL,GAAEA,KAAQ+C,IAEV,GAAE/C,KAAQ+C,KAGtBsE,EAAK2T,aAAa,SAAUhb,GAE5BqH,EAAK2T,aAAa,KAAM,gBACxB3T,EAAK2T,aAAa,QAAS,kBAC3B3T,EAAK2T,aAAa,OAAQ,gBAC1B3T,EAAK2T,aAAa,SAAUvZ,EAAQqB,QAAU,QAC9CuE,EAAK2T,aAAa,SAAUvZ,EAAQmQ,QAAU,UAE9C9Q,SAASma,KAAKC,YAAY7T,GAE1B,IAAK,IAAIxI,KAAQmD,EACf,GAAkD,kBAA9CQ,OAAOoX,UAAUuB,SAASC,KAAKpZ,EAAKnD,IAEtCmD,EAAKnD,GAAMqE,KAAKX,IACd,IAAI8Y,EAAQva,SAASia,cAAc,SACnCM,EAAML,aAAa,OAAQ,QAC3BK,EAAML,aAAa,OAAS,GAAEnc,KAC9Bwc,EAAML,aAAa,QAAS/W,SAAS1B,IACrC8E,EAAK6T,YAAYG,EAAjB,QAEG,CACL,GAAmB,OAAfrZ,EAAKnD,GAAgB,SACzB,IAAIwc,EAAQva,SAASia,cAAc,SACnCM,EAAML,aAAa,OAAQ,QAC3BK,EAAML,aAAa,OAAQnc,GAC3Bwc,EAAML,aAAa,QAAShZ,EAAKnD,IACjCwI,EAAK6T,YAAYG,EAClB,CAEHhU,EAAKiU,SACLxa,SAASma,KAAKM,YAAYlU,GAE1BoO,EAAQwE,OArDV,C,2ICDO,MAEMpC,EAAO,0BAAG,YAOnB,OAAOC,MAAQA,IAAIC,SAAW,aAEjC,IATmB,qDAWPC,EAAkB,0BAAG,UAAOC,GACvC,MAAMjW,EAAOiW,EAAEjW,KACf,GAAKiW,EAAEjW,MAeA,GAAkB,cAAdA,EAAKkW,KACdC,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAASrW,EAAKsW,gBAEX,GAAItW,EAAKuW,OAA6B,IAApBvW,EAAKuW,MAAML,KAClCC,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAAS,gCAEN,GAAIrW,EAAKiB,QAA+B,YAArBjB,EAAKiB,OAAOiV,MACX,iBAArBlW,EAAKiB,OAAOiV,KACd,OAAOL,QA3BS,wCAAdI,EAAEI,QACJF,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAAS,qBAGXF,EAAAA,EAAAA,MAAa,CACXzX,MAAO,OACP0X,SAAU,IACVC,QAAS,2BAoBf,MAAO,EAAC,EACT,IAnC8B,sDAsClBG,EAAsB,0BAAG,UAAOP,GAE3C,OAAIA,EAAEjW,KAAK+J,QAAQ,sBAAwB,GAIvCkM,EAAEQ,QAAQC,YAAY3M,QAAQ,aAAe,EAHxC8L,IAMF,EAAC,EACT,IAVkC,sDChDjCc,IAAAA,SAAAA,iBAAiC,EAGnC,gCAAe,aAA6E,QAApE7V,OAAAA,OAAoE,MAA3D,MAA2D,EAApD9C,EAAoD,EAApDA,KAAoD,IAA9C+C,OAAAA,OAA8C,MAArC,KAAqC,MAA/Bf,KAAAA,OAA+B,MAAxB,KAAwB,EAAlBgH,EAAkB,EAAlBA,YACxE,IACM4P,EAIJ7V,EAASP,OAAOC,OAAO,CAAC,EAAGM,EAAQ,CAAE8V,EAAGpG,KAAKqG,WAC7C/V,EAAoB,QAAXD,EAAmBN,OAAOC,OAAO,CAAC,EAAGM,EAAQf,GAAQe,EAE9D,IAAIgW,EAAoB,kCACJ,SAAhB/P,EACF+P,EAAoB,kCACK,SAAhB/P,IACT+P,EAAoB,oDAGlB/W,GAAwB,SAAhBgH,GAA0B,iBAAiBjJ,KAAK+C,KAC1Dd,EAAOgX,IAAAA,UAAahX,IAEtB,MAAMgB,QAAY2V,IAAM,CACtB7V,OAAQA,EACRmW,IAAK,IAAMjZ,EACXkZ,QACoF,KACpFnW,OAAQA,EACRoW,QAAS,CACP,eAAgBJ,EAChBK,OAAQ,OAEVpX,KAAM,iBAAiBjC,KAAK+C,GAAUd,EAAO,KAG/C,OAAIgB,EAAImW,QAAQ,gBAAgBpN,QAAQ,cAAgB,SAChDyM,EAAuBxV,GACtB,EAAC,EAAOA,EAAIhB,OAKlBgB,EAAIhB,MAAQgB,EAAIhB,KAAKkW,MAAQ,CAAC,UAAW,QAAQnM,QAAQ/I,EAAIhB,KAAKkW,MAAQ,GAC1ElV,EAAIhB,MAAQgB,EAAIhB,KAAKuW,OACrBvV,EAAIhB,MACHgB,EAAIhB,KAAKiB,QACTD,EAAIhB,KAAKiB,OAAOiV,MAChB,CAAC,UAAW,QAAQnM,QAAQ/I,EAAIhB,KAAKiB,OAAOiV,MAAQ,SAEhDF,EAAmBhV,GAClB,EAAC,EAAOA,EAAIhB,OAGd,EAAC,EAAMgB,EAAIhB,KAInB,CAHC,MAAOiW,GAEP,aADMD,EAAmBC,GAClB,EAAC,EAAOA,EAAEjW,KAClB,CACF,IAvDD,sDCNA,G,uBCFA,IAAIkB,EAAM,CACT,qCAAsC,KACtC,0CAA2C,KAC3C,mDAAoD,KACpD,wDAAyD,KACzD,mDAAoD,KACpD,2DAA4D,KAC5D,yCAA0C,KAC1C,mCAAoC,KACpC,oCAAqC,KACrC,wDAAyD,KACzD,mDAAoD,KACpD,oDAAqD,MAItD,SAASsY,EAAeC,GACvB,IAAI7S,EAAK8S,EAAsBD,GAC/B,OAAOE,EAAoB/S,EAC5B,CACA,SAAS8S,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAE1Y,EAAKuY,GAAM,CACpC,IAAIxD,EAAI,IAAI4D,MAAM,uBAAyBJ,EAAM,KAEjD,MADAxD,EAAEC,KAAO,mBACHD,CACP,CACA,OAAO/U,EAAIuY,EACZ,CACAD,EAAe7U,KAAO,WACrB,OAAOnE,OAAOmE,KAAKzD,EACpB,EACAsY,EAAexU,QAAU0U,EACzB/c,EAAOC,QAAU4c,EACjBA,EAAe5S,GAAK,I,GChChBkT,EAA2B,CAAC,EAGhC,SAASH,EAAoBI,GAE5B,IAAIC,EAAeF,EAAyBC,GAC5C,QAAqBrJ,IAAjBsJ,EACH,OAAOA,EAAapd,QAGrB,IAAID,EAASmd,EAAyBC,GAAY,CAGjDnd,QAAS,CAAC,GAOX,OAHAqd,EAAoBF,GAAUX,KAAKzc,EAAOC,QAASD,EAAQA,EAAOC,QAAS+c,GAGpEhd,EAAOC,OACf,CAGA+c,EAAoBO,EAAID,E,WCzBxB,IAAIE,EAAW,GACfR,EAAoBS,EAAI,SAASnZ,EAAQoZ,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIP,EAASzW,OAAQgX,IAAK,CACrCL,EAAWF,EAASO,GAAG,GACvBJ,EAAKH,EAASO,GAAG,GACjBH,EAAWJ,EAASO,GAAG,GAE3B,IAJA,IAGIC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAAS3W,OAAQkX,MACpB,EAAXL,GAAsBC,GAAgBD,IAAa/Z,OAAOmE,KAAKgV,EAAoBS,GAAGS,OAAM,SAASpD,GAAO,OAAOkC,EAAoBS,EAAE3C,GAAK4C,EAASO,GAAK,IAChKP,EAASS,OAAOF,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbR,EAASW,OAAOJ,IAAK,GACrB,IAAI7D,EAAIyD,SACE5J,IAANmG,IAAiB5V,EAAS4V,EAC/B,CACD,CACA,OAAO5V,CArBP,CAJCsZ,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIP,EAASzW,OAAQgX,EAAI,GAAKP,EAASO,EAAI,GAAG,GAAKH,EAAUG,IAAKP,EAASO,GAAKP,EAASO,EAAI,GACrGP,EAASO,GAAK,CAACL,EAAUC,EAAIC,EAwB/B,C,eC5BAZ,EAAoBoB,EAAI,SAASpe,GAChC,IAAIqe,EAASre,GAAUA,EAAOse,WAC7B,WAAa,OAAOte,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAgd,EAAoBuB,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNArB,EAAoBuB,EAAI,SAASte,EAASwe,GACzC,IAAI,IAAI3D,KAAO2D,EACXzB,EAAoBC,EAAEwB,EAAY3D,KAASkC,EAAoBC,EAAEhd,EAAS6a,IAC5EjX,OAAO6a,eAAeze,EAAS6a,EAAK,CAAE6D,YAAY,EAAMC,IAAKH,EAAW3D,IAG3E,C,eCPAkC,EAAoB6B,EAAI,CAAC,EAGzB7B,EAAoB1D,EAAI,SAASwF,GAChC,OAAOC,QAAQ1Y,IAAIxC,OAAOmE,KAAKgV,EAAoB6B,GAAGG,QAAO,SAASC,EAAUnE,GAE/E,OADAkC,EAAoB6B,EAAE/D,GAAKgE,EAASG,GAC7BA,CACR,GAAG,IACJ,C,eCPAjC,EAAoBkC,EAAI,SAASJ,GAEhC,MAAO,MAAQA,EAAU,IAAM,CAAC,GAAK,WAAW,GAAK,WAAW,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,KACpN,C,eCHA9B,EAAoBmC,SAAW,SAASL,GAEvC,MAAO,OAASA,EAAU,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,MAClH,C,eCJA9B,EAAoBoC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO/e,MAAQ,IAAIgf,SAAS,cAAb,EAGhB,CAFE,MAAOhG,GACR,GAAsB,kBAAXvR,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBiV,EAAoBC,EAAI,SAASsC,EAAKC,GAAQ,OAAO3b,OAAOoX,UAAUwE,eAAehD,KAAK8C,EAAKC,EAAO,C,eCAtG,IAAIE,EAAa,CAAC,EACdC,EAAoB,uBAExB3C,EAAoB4C,EAAI,SAAStF,EAAK5C,EAAMoD,EAAKgE,GAChD,GAAGY,EAAWpF,GAAQoF,EAAWpF,GAAKrY,KAAKyV,OAA3C,CACA,IAAImI,EAAQC,EACZ,QAAW/L,IAAR+G,EAEF,IADA,IAAIiF,EAAU5d,SAAS6d,qBAAqB,UACpCjC,EAAI,EAAGA,EAAIgC,EAAQhZ,OAAQgX,IAAK,CACvC,IAAIkC,EAAIF,EAAQhC,GAChB,GAAGkC,EAAEC,aAAa,QAAU5F,GAAO2F,EAAEC,aAAa,iBAAmBP,EAAoB7E,EAAK,CAAE+E,EAASI,EAAG,KAAO,CACpH,CAEGJ,IACHC,GAAa,EACbD,EAAS1d,SAASia,cAAc,UAEhCyD,EAAOM,QAAU,QACjBN,EAAOO,QAAU,IACbpD,EAAoBqD,IACvBR,EAAOxD,aAAa,QAASW,EAAoBqD,IAElDR,EAAOxD,aAAa,eAAgBsD,EAAoB7E,GACxD+E,EAAOS,IAAMhG,GAEdoF,EAAWpF,GAAO,CAAC5C,GACnB,IAAI6I,EAAmB,SAASC,EAAMC,GAErCZ,EAAOa,QAAUb,EAAOc,OAAS,KACjCC,aAAaR,GACb,IAAIS,EAAUnB,EAAWpF,GAIzB,UAHOoF,EAAWpF,GAClBuF,EAAOiB,YAAcjB,EAAOiB,WAAWlE,YAAYiD,GACnDgB,GAAWA,EAAQhf,SAAQ,SAAS8b,GAAM,OAAOA,EAAG8C,EAAQ,IACzDD,EAAM,OAAOA,EAAKC,EACtB,EACIL,EAAU1E,WAAW6E,EAAiBQ,KAAK,UAAMhN,EAAW,CAAErH,KAAM,UAAWuG,OAAQ4M,IAAW,MACtGA,EAAOa,QAAUH,EAAiBQ,KAAK,KAAMlB,EAAOa,SACpDb,EAAOc,OAASJ,EAAiBQ,KAAK,KAAMlB,EAAOc,QACnDb,GAAc3d,SAAS6e,KAAKzE,YAAYsD,EAnCkB,CAoC3D,C,eCvCA7C,EAAoB9C,EAAI,SAASja,GACX,qBAAXghB,QAA0BA,OAAOC,aAC1Crd,OAAO6a,eAAeze,EAASghB,OAAOC,YAAa,CAAE1c,MAAO,WAE7DX,OAAO6a,eAAeze,EAAS,aAAc,CAAEuE,OAAO,GACvD,C,eCNAwY,EAAoBmE,EAAI,E,eCAxB,IAAIC,EAAmB,SAAStC,EAASuC,EAAUhZ,EAASiZ,GAC3D,IAAIC,EAAUpf,SAASia,cAAc,QAErCmF,EAAQC,IAAM,aACdD,EAAQ7U,KAAO,WACf,IAAI+U,EAAiB,SAAShB,GAG7B,GADAc,EAAQb,QAAUa,EAAQZ,OAAS,KAChB,SAAfF,EAAM/T,KACTrE,QACM,CACN,IAAIqZ,EAAYjB,IAAyB,SAAfA,EAAM/T,KAAkB,UAAY+T,EAAM/T,MAChEiV,EAAWlB,GAASA,EAAMxN,QAAUwN,EAAMxN,OAAO2O,MAAQP,EACzDQ,EAAM,IAAI3E,MAAM,qBAAuB4B,EAAU,cAAgB6C,EAAW,KAChFE,EAAItI,KAAO,wBACXsI,EAAInV,KAAOgV,EACXG,EAAI/H,QAAU6H,EACdJ,EAAQT,WAAWlE,YAAY2E,GAC/BD,EAAOO,EACR,CACD,EAKA,OAJAN,EAAQb,QAAUa,EAAQZ,OAASc,EACnCF,EAAQK,KAAOP,EAEflf,SAAS6e,KAAKzE,YAAYgF,GACnBA,CACR,EACIO,EAAiB,SAASF,EAAMP,GAEnC,IADA,IAAIU,EAAmB5f,SAAS6d,qBAAqB,QAC7CjC,EAAI,EAAGA,EAAIgE,EAAiBhb,OAAQgX,IAAK,CAChD,IAAIiE,EAAMD,EAAiBhE,GACvBkE,EAAWD,EAAI9B,aAAa,cAAgB8B,EAAI9B,aAAa,QACjE,GAAe,eAAZ8B,EAAIR,MAAyBS,IAAaL,GAAQK,IAAaZ,GAAW,OAAOW,CACrF,CACA,IAAIE,EAAoB/f,SAAS6d,qBAAqB,SACtD,IAAQjC,EAAI,EAAGA,EAAImE,EAAkBnb,OAAQgX,IAAK,CAC7CiE,EAAME,EAAkBnE,GACxBkE,EAAWD,EAAI9B,aAAa,aAChC,GAAG+B,IAAaL,GAAQK,IAAaZ,EAAU,OAAOW,CACvD,CACD,EACIG,EAAiB,SAASrD,GAC7B,OAAO,IAAIC,SAAQ,SAAS1W,EAASiZ,GACpC,IAAIM,EAAO5E,EAAoBmC,SAASL,GACpCuC,EAAWrE,EAAoBmE,EAAIS,EACvC,GAAGE,EAAeF,EAAMP,GAAW,OAAOhZ,IAC1C+Y,EAAiBtC,EAASuC,EAAUhZ,EAASiZ,EAC9C,GACD,EAEIc,EAAqB,CACxB,IAAK,GAGNpF,EAAoB6B,EAAEwD,QAAU,SAASvD,EAASG,GACjD,IAAIqD,EAAY,CAAC,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,GAC5CF,EAAmBtD,GAAUG,EAAShd,KAAKmgB,EAAmBtD,IACzB,IAAhCsD,EAAmBtD,IAAkBwD,EAAUxD,IACtDG,EAAShd,KAAKmgB,EAAmBtD,GAAWqD,EAAerD,GAASyD,MAAK,WACxEH,EAAmBtD,GAAW,CAC/B,IAAG,SAASxF,GAEX,aADO8I,EAAmBtD,GACpBxF,CACP,IAEF,C,eC5DA,IAAIkJ,EAAkB,CACrB,IAAK,GAGNxF,EAAoB6B,EAAEZ,EAAI,SAASa,EAASG,GAE1C,IAAIwD,EAAqBzF,EAAoBC,EAAEuF,EAAiB1D,GAAW0D,EAAgB1D,QAAW/K,EACtG,GAA0B,IAAvB0O,EAGF,GAAGA,EACFxD,EAAShd,KAAKwgB,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAI3D,SAAQ,SAAS1W,EAASiZ,GAAUmB,EAAqBD,EAAgB1D,GAAW,CAACzW,EAASiZ,EAAS,IACzHrC,EAAShd,KAAKwgB,EAAmB,GAAKC,GAGtC,IAAIpI,EAAM0C,EAAoBmE,EAAInE,EAAoBkC,EAAEJ,GAEpDlF,EAAQ,IAAIsD,MACZyF,EAAe,SAASlC,GAC3B,GAAGzD,EAAoBC,EAAEuF,EAAiB1D,KACzC2D,EAAqBD,EAAgB1D,GACX,IAAvB2D,IAA0BD,EAAgB1D,QAAW/K,GACrD0O,GAAoB,CACtB,IAAIf,EAAYjB,IAAyB,SAAfA,EAAM/T,KAAkB,UAAY+T,EAAM/T,MAChEkW,EAAUnC,GAASA,EAAMxN,QAAUwN,EAAMxN,OAAOqN,IACpD1G,EAAMF,QAAU,iBAAmBoF,EAAU,cAAgB4C,EAAY,KAAOkB,EAAU,IAC1FhJ,EAAM1Z,KAAO,iBACb0Z,EAAMlN,KAAOgV,EACb9H,EAAME,QAAU8I,EAChBH,EAAmB,GAAG7I,EACvB,CAEF,EACAoD,EAAoB4C,EAAEtF,EAAKqI,EAAc,SAAW7D,EAASA,EAE/D,CAEH,EAUA9B,EAAoBS,EAAEQ,EAAI,SAASa,GAAW,OAAoC,IAA7B0D,EAAgB1D,EAAgB,EAGrF,IAAI+D,EAAuB,SAASC,EAA4Bzf,GAC/D,IAKI+Z,EAAU0B,EALVpB,EAAWra,EAAK,GAChB0f,EAAc1f,EAAK,GACnB2f,EAAU3f,EAAK,GAGI0a,EAAI,EAC3B,GAAGL,EAASuF,MAAK,SAAShZ,GAAM,OAA+B,IAAxBuY,EAAgBvY,EAAW,IAAI,CACrE,IAAImT,KAAY2F,EACZ/F,EAAoBC,EAAE8F,EAAa3F,KACrCJ,EAAoBO,EAAEH,GAAY2F,EAAY3F,IAGhD,GAAG4F,EAAS,IAAI1e,EAAS0e,EAAQhG,EAClC,CAEA,IADG8F,GAA4BA,EAA2Bzf,GACrD0a,EAAIL,EAAS3W,OAAQgX,IACzBe,EAAUpB,EAASK,GAChBf,EAAoBC,EAAEuF,EAAiB1D,IAAY0D,EAAgB1D,IACrE0D,EAAgB1D,GAAS,KAE1B0D,EAAgB1D,GAAW,EAE5B,OAAO9B,EAAoBS,EAAEnZ,EAC9B,EAEI4e,EAAqBC,KAAK,mCAAqCA,KAAK,oCAAsC,GAC9GD,EAAmBrhB,QAAQghB,EAAqB9B,KAAK,KAAM,IAC3DmC,EAAmBjhB,KAAO4gB,EAAqB9B,KAAK,KAAMmC,EAAmBjhB,KAAK8e,KAAKmC,G,ICpFvF,IAAIE,EAAsBpG,EAAoBS,OAAE1J,EAAW,CAAC,MAAM,WAAa,OAAOiJ,EAAoB,KAAO,IACjHoG,EAAsBpG,EAAoBS,EAAE2F,E", "sources": ["webpack://vue-chevron-desktop/./src/components/customize/files/register.conf.js", "webpack://vue-chevron-desktop/./src/components/customize/popconfirm/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/brand/brand-by-channel/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/dealer/dealer-by-resourceId/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/dealer/dealer-by-sales/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/dealer/retailer-by-distributor/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/dict-options/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/number/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/options/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/region/region-by-resourceId/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/user/dsr-by-resourceId/register.conf.js", "webpack://vue-chevron-desktop/./src/components/select/user/user-by-resourceId/register.conf.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/app.vue?28fd", "webpack://vue-chevron-desktop/src/projects/market/resource-application-2021/app.vue", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/app.vue?9ffb", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/app.vue", "webpack://vue-chevron-desktop/./src/resources/router/hooks/afterEach/doc-title-replacer.js", "webpack://vue-chevron-desktop/./src/resources/router/hooks/index.js", "webpack://vue-chevron-desktop/./src/resources/router/index.js", "webpack://vue-chevron-desktop/./src/resources/store/modules/dict-options.js", "webpack://vue-chevron-desktop/./src/resources/store/modules/permission.js", "webpack://vue-chevron-desktop/./src/resources/store/modules/user.js", "webpack://vue-chevron-desktop/./src/resources/store/index.js", "webpack://vue-chevron-desktop/./src/resources/add-on/math/index.js", "webpack://vue-chevron-desktop/./src/resources/add-on/elements/index.js", "webpack://vue-chevron-desktop/./src/resources/filters/_func/money.js", "webpack://vue-chevron-desktop/./src/resources/filters/index.js", "webpack://vue-chevron-desktop/./src/resources/add-on/index.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/filter/index.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/main.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/service/apply.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/service/common.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/local-signage.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/local-seminar.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/local-ck.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/request-signage.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/request-seminar.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/request-ck.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-ck-local.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-request-att.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-ck-request.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-signage-local.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-signage-request.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-seminar-local.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-seminar-request.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/apply/index.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/ck_products/index.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/dialog.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/global.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/list.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/products/_values/package.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/products/_values/delo.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/resources/storeModules/products/index.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/routes.conf.js", "webpack://vue-chevron-desktop/./src/projects/market/resource-application-2021/stores.conf.js", "webpack://vue-chevron-desktop/./src/resources/xhr/config.js", "webpack://vue-chevron-desktop/./src/resources/xhr/axios.js", "webpack://vue-chevron-desktop/./src/resources/xhr/index.js", "webpack://vue-chevron-desktop/./src/resources/utils/tools/download-async.js", "webpack://vue-chevron-desktop/./src/resources/service/core.js", "webpack://vue-chevron-desktop/./src/resources/utils/dialog/notify/index.js", "webpack://vue-chevron-desktop/./src/resources/utils/tools/download.js", "webpack://vue-chevron-desktop/./src/resources/utils/xhr/config.js", "webpack://vue-chevron-desktop/./src/resources/utils/xhr/axios.js", "webpack://vue-chevron-desktop/./src/resources/utils/xhr/index.js", "webpack://vue-chevron-desktop/./src/components/ sync \\/register\\.conf\\.js$", "webpack://vue-chevron-desktop/webpack/bootstrap", "webpack://vue-chevron-desktop/webpack/runtime/chunk loaded", "webpack://vue-chevron-desktop/webpack/runtime/compat get default export", "webpack://vue-chevron-desktop/webpack/runtime/define property getters", "webpack://vue-chevron-desktop/webpack/runtime/ensure chunk", "webpack://vue-chevron-desktop/webpack/runtime/get javascript chunk filename", "webpack://vue-chevron-desktop/webpack/runtime/get mini-css chunk filename", "webpack://vue-chevron-desktop/webpack/runtime/global", "webpack://vue-chevron-desktop/webpack/runtime/hasOwnProperty shorthand", "webpack://vue-chevron-desktop/webpack/runtime/load script", "webpack://vue-chevron-desktop/webpack/runtime/make namespace object", "webpack://vue-chevron-desktop/webpack/runtime/publicPath", "webpack://vue-chevron-desktop/webpack/runtime/css loading", "webpack://vue-chevron-desktop/webpack/runtime/jsonp chunk loading", "webpack://vue-chevron-desktop/webpack/startup"], "sourcesContent": ["module.exports = {\r\n  name: \"el-upload-customize\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "module.exports = {\r\n  name: \"el-popconfirm-customize\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-03-19 14:56\r\n * 根据渠道筛选品牌\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-brand-by-channel\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-01-15 11:07\r\n * 该组件通过参数获取经销商数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-dealer-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-01-15 11:07\r\n * 该组件通过参数获取经销商数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-dealer\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * 该组件通过参数获取经分销商数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-retailer\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-01-15 10:48\r\n * 该组件通过请求数据字典表接口获得选项内容\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-dict-options\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-01-15 14:33\r\n * 该组件可以选择年份\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-number\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-01-19 14:38\r\n * 该组件允许传输自定义方法获取下拉选项，或者直接传输 options 作为选项\r\n * getOptions 与 options 同时存在时，取两者的并集\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-options\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-03-15 17:09\r\n * 该组件通过参数获取区域\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-region-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-04-07 11:08\r\n * 该组件通过参数获取用户数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-dsr-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-04-07 11:08\r\n * 该组件通过参数获取用户数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-user-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  watch: {\r\n    // eslint-disable-next-line no-unused-vars\r\n    $route(to, from) {\r\n      let globalConfig = {};\r\n      if (to.query.executor) {\r\n        globalConfig.executor = to.query.executor;\r\n      }\r\n      if (/^\\/signage\\/.*$/.test(to.path)) {\r\n        globalConfig.salesChannel = \"signage\";\r\n      }\r\n      if (/^\\/seminar\\/.*$/.test(to.path)) {\r\n        globalConfig.salesChannel = \"seminar\";\r\n      }\r\n      if (/^\\/ck\\/.*$/.test(to.path)) {\r\n        globalConfig.salesChannel = \"ck\";\r\n      }\r\n      this.$store.commit(\"SET_GLOBAL\", globalConfig);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\nbody {\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n  margin: 0;\r\n  background-color: #f3f3f4;\r\n  padding: 15px;\r\n}\r\n#app {\r\n  box-sizing: border-box;\r\n  padding: 15px;\r\n  background-color: #fff;\r\n  min-width: 600px;\r\n  max-width: 1800px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??clonedRuleSet-40.use[0]!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../../../../../Users/<USER>/AppData/Roaming/npm/node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./app.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??clonedRuleSet-40.use[0]!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../../../../../Users/<USER>/AppData/Roaming/npm/node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./app.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./app.vue?vue&type=template&id=6f41539e&\"\nimport script from \"./app.vue?vue&type=script&lang=js&\"\nexport * from \"./app.vue?vue&type=script&lang=js&\"\nimport style0 from \"./app.vue?vue&type=style&index=0&id=6f41539e&prod&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Users/<USER>/AppData/Roaming/npm/node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "/**\r\n * replace document title\r\n */\r\nexport default to => {\r\n  let titles = [];\r\n  let matched = to.matched;\r\n\r\n  matched.slice().forEach(handler => {\r\n    let title = handler.meta.title;\r\n    title && titles.push(title);\r\n  });\r\n\r\n  let title = titles.join(\" · \");\r\n  document.title = title;\r\n};\r\n", "import docTitleReplacer from \"./afterEach/doc-title-replacer.js\";\r\n\r\nexport default router => {\r\n  router.afterEach(docTitleReplacer);\r\n};\r\n", "import Vue from \"vue\";\r\nimport hooks from \"./hooks\";\r\nimport Router from \"vue-router\";\r\n\r\nVue.use(Router);\r\nconst router = new Router({\r\n  mode: \"hash\",\r\n  routes: []\r\n});\r\n\r\nhooks(router);\r\n\r\nexport default router;\r\n", "import coreService from \"@resources/service/core\";\r\n\r\nconst timeInterval = 300000;\r\n\r\nconst state = {\r\n  options: [],\r\n};\r\n\r\nconst getters = {\r\n  getOptions(state) {\r\n    return (dictName) => {\r\n      return state.options.find((options) => options.name === dictName) || {};\r\n    };\r\n  },\r\n  getOptionsData(_, getters) {\r\n    return (dictName) => {\r\n      const options = getters.getOptions(dictName);\r\n      return options ? options.data : [];\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  UPDATE_OPTIONS(state, payload) {\r\n    // 刷新更新时间\r\n    payload.updateTime = new Date().getTime();\r\n\r\n    const options = state.options.find((item) => {\r\n      return item.name === payload.name && Object.assign(item, payload);\r\n    });\r\n    !options && state.options.push(payload);\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getDictOptions({ commit, getters }, dictName) {\r\n    const options = getters.getOptions(dictName);\r\n    if (options && new Date().getTime() - options.updateTime <= timeInterval) {\r\n      // 小于等于 timeInterval 的时间间隔内不会重新获取\r\n      return [true, options];\r\n    } else {\r\n      commit(\"UPDATE_OPTIONS\", { name: dictName, status: \"loading\", data: [] });\r\n    }\r\n\r\n    const [status, res] = await coreService.requestByRPC({\r\n      method: \"dicService.getDicItemByDicTypeCode\",\r\n      params: [dictName],\r\n    });\r\n    if (status) {\r\n      commit(\"UPDATE_OPTIONS\", {\r\n        name: dictName,\r\n        status: \"loaded\",\r\n        data: res.result.data.map((item) => ({\r\n          value: \"\" + item.dicItemCode,\r\n          label: item.dicItemName,\r\n        })),\r\n      });\r\n    }\r\n    return [status, getters.getOptions(dictName)];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import coreService from \"@resources/service/core\";\r\n\r\nconst timeInterval = 300000;\r\n\r\nconst state = {\r\n  permissions: [],\r\n};\r\n\r\nconst getters = {\r\n  getPermission(state) {\r\n    // 获取权限对象\r\n    return (permissionName) => {\r\n      return state.permissions.find((permission) => permission.name === permissionName) || {};\r\n    };\r\n  },\r\n  getPermissionData(_, getters) {\r\n    // 获取权限对象的值\r\n    return (permissionName) => {\r\n      const permission = getters.getPermission(permissionName);\r\n      return permission ? permission.data : 0;\r\n    };\r\n  },\r\n  hasPermission(_, getters) {\r\n    // 权限值是否包含 payload 参数\r\n    return (permissionName) => {\r\n      const permission = getters.getPermission(permissionName);\r\n      return (payload) => {\r\n        let weight = permission.data;\r\n        let bit = 0;\r\n\r\n        if (weight == -1) return true; // admin 获得所有权限\r\n\r\n        payload = math.log(payload, 2) + 1;\r\n\r\n        if (payload > 32) {\r\n          if (weight >= 4294967296) {\r\n            weight = parseInt(weight / 4294967296);\r\n            payload = payload - 32;\r\n            bit = weight >>> (payload - 1);\r\n          } else {\r\n            bit = 0;\r\n          }\r\n        } else {\r\n          bit = weight >>> (payload - 1);\r\n        }\r\n\r\n        return !!(payload && !isNaN(payload - 1) && !!(bit & 1));\r\n      };\r\n    };\r\n  },\r\n  hasPermissionNotAdmin(_, getters) {\r\n    // 权限值是否包含 payload 参数，并且排除 admin 角色\r\n    // 例如：权限值包含 8 时，就执行某操作，但是 admin 除外\r\n    return (permissionName) => {\r\n      const permission = getters.getPermission(permissionName);\r\n      const hasPermission = getters.hasPermission(permissionName);\r\n      return (payload) => {\r\n        return permission.data != -1 && hasPermission(payload);\r\n      };\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  UPDATE_PERMISSION(state, payload) {\r\n    // 刷新更新时间\r\n    payload.updateTime = new Date().getTime();\r\n\r\n    const permission = state.permissions.find((item) => {\r\n      return item.name === payload.name && Object.assign(item, payload);\r\n    });\r\n    !permission && state.permissions.push(payload);\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getOperationPermissionByUser({ commit, getters }, permissionName) {\r\n    const permission = getters.getPermission(permissionName);\r\n    if (permission && new Date().getTime() - permission.updateTime <= timeInterval) {\r\n      // 小于等于 timeInterval 的时间间隔内不会重新获取\r\n      return [true, permission];\r\n    } else {\r\n      commit(\"UPDATE_PERMISSION\", { name: permissionName, status: \"loading\", data: [] });\r\n    }\r\n\r\n    const [status, res] = await coreService.requestByRPC({\r\n      method: \"operationPermissionService.getOperationPermissionByUser\",\r\n      params: [null, permissionName],\r\n    });\r\n    if (status) {\r\n      commit(\"UPDATE_PERMISSION\", {\r\n        name: permissionName,\r\n        status: \"loaded\",\r\n        data: res.result.weight,\r\n      });\r\n    }\r\n    return [status, getters.getPermission(permissionName)];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import coreService from \"@resources/service/core\";\r\n\r\nconst state = {\r\n  currentUser: {},\r\n};\r\n\r\nconst getters = {\r\n  getCurrentUser(state) {\r\n    // 获取当前用户\r\n    return state.currentUser;\r\n  },\r\n  currentUser(state) {\r\n    // 获取当前用户\r\n    return state.currentUser;\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  UPDATE_CURRENT_USER(state, payload) {\r\n    state.currentUser = payload;\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getCurrentUserInfo({ commit, getters }) {\r\n    const [status, res] = await coreService.requestByRPC({\r\n      method: \"userService.getLoginUser\",\r\n      params: [],\r\n    });\r\n    if (status) {\r\n      commit(\"UPDATE_CURRENT_USER\", res.result.data);\r\n    }\r\n    return [status, getters.getCurrentUser];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import Vue from \"vue\";\r\nimport Vuex from \"vuex\";\r\nimport dictOptions from \"./modules/dict-options\";\r\nimport permission from \"./modules/permission\";\r\nimport user from \"./modules/user\";\r\n\r\nVue.use(Vuex);\r\n\r\nexport default new Vuex.Store({\r\n  modules: {\r\n    dictOptions,\r\n    permission,\r\n    user,\r\n  },\r\n});\r\n", "import { create, all } from 'mathjs'\r\n\r\nconst config = { }\r\nconst math = create(all, config)\r\n\r\nexport default math", "import Vue from \"vue\";\r\nimport ElementUI, { Loading } from \"element-ui\";\r\n\r\nimport \"./scss/index.scss\";\r\n\r\nVue.use(ElementUI);\r\n\r\nVue.$loading = Loading.service;\r\n", "const config = {\r\n  decimal: \".\",\r\n  thousands: \",\",\r\n  prefix: \"\",\r\n  suffix: \"\",\r\n  precision: 2\r\n};\r\n\r\nexport function numberToThousand(number = \"\") {\r\n  number = number === null ? \"\" : \"\" + number;\r\n  if (number.length > config.precision) {\r\n    number = number.split(config.decimal);\r\n    number[0] = number[0]\r\n      .split(config.thousands)\r\n      .join(\"\")\r\n      .replace(/\\B(?=(?:\\d{3})+\\b)/g, config.thousands);\r\n    number[1] = number[1] || \"\";\r\n    number[1] =\r\n      number[1].length > config.precision ? number[1].slice(0, config.precision) : number[1];\r\n    // number[1] = number[1].length < config.precision ? number[1] + '000000000000000'.slice(0, config.precision - number[1].length) :number[1]\r\n    if (number[1] >= 1) {\r\n      number[1] = config.decimal + number[1];\r\n    } else {\r\n      number[1] = \"\";\r\n    }\r\n    number = number[0] + number[1];\r\n  } else if (number === \"\") {\r\n    return \"\";\r\n  } else {\r\n    number = number.split(config.decimal);\r\n    number[1] = number[1] || \"\";\r\n    number[1] =\r\n      number[1].length > config.precision ? number[1].slice(0, config.precision) : number[1];\r\n    // number[1] = number[1].length < config.precision ? number[1] + '000000000000000'.slice(0, config.precision - number[1].length) :number[1]\r\n    if (number[1] >= 1) {\r\n      number[1] = config.decimal + number[1];\r\n    }\r\n    number = number[0] + number[1];\r\n  }\r\n  return config.prefix + number + config.suffix;\r\n}\r\nexport function thousandToNumber(money = \"\") {\r\n  if (money === \"0.\" + \"000000000000000\".slice(0, config.precision - 1)) {\r\n    return \"\";\r\n  } else if (money.length === 1) {\r\n    money = \"000000000000000\".slice(0, config.precision) + money;\r\n  } else if (!/\\./.test(money)) {\r\n    money += \"000000000000000\".slice(0, config.precision);\r\n  }\r\n  money = money\r\n    .split(config.decimal)\r\n    .join(\"\")\r\n    .split(config.thousands)\r\n    .join(\"\")\r\n    .replace(/^0+/, \"\")\r\n    .replace(/[^\\d]/g, \"\");\r\n\r\n  if (money.length > config.precision) {\r\n    money = money.replace(new RegExp(\"(\\\\d{\" + config.precision + \"})$\"), config.decimal + \"$1\");\r\n  } else {\r\n    money = (money / Math.pow(10, config.precision)).toFixed(config.precision);\r\n  }\r\n  return money;\r\n}\r\n", "import vue from \"vue\";\r\nimport dayjs from \"dayjs\";\r\nimport { numberToThousand } from \"./_func/money\";\r\n\r\nvue.filter(\"toMoney\", (val) => {\r\n  if (val === null) return \"\";\r\n  if (val === \"\") return \"\";\r\n  const data = Number(val);\r\n  return math.isNaN(data) ? val : numberToThousand(data);\r\n});\r\n\r\nvue.filter(\"toRound\", (val) => {\r\n  const data = Number(val);\r\n  return math.isNaN(data) ? val : math.round(data);\r\n});\r\n\r\nvue.filter(\"zeroToString\", (val) => {\r\n  const data = Number(val);\r\n  return math.isNaN(data) ? val : data === 0 ? \"\" : val;\r\n});\r\n\r\nvue.filter(\"dayjs\", (val, fmt = \"YYYY-MM-DD HH:mm\") => {\r\n  return val ? dayjs(val).format(fmt) : \"\";\r\n});\r\n\r\nvue.filter(\"dictOptions\", (val, dictName) => {\r\n  let options = vue.$store.getters.getOptions(dictName);\r\n  if (!options) return val;\r\n\r\n  const option = options.data.find((item) => \"\" + item.value === \"\" + val);\r\n  return option ? option.label : val;\r\n});\r\n", "/**\r\n * update: 2021-01-13 15:27\r\n *\r\n * 2021 年之后的项目从这里加载，逐渐替代原来的 /utils/add-on\r\n * 为了将来前端独立打包做准备\r\n */\r\nimport vue from \"vue\";\r\nimport router from \"@resources/router\";\r\nimport store from \"@resources/store\";\r\n// 加载函数库，主要解决金额的计算\r\nimport math from \"./math\";\r\n// 加载需要的界面组件\r\nimport \"./elements\";\r\n// 加载全局过滤器\r\nimport \"@resources/filters\";\r\n\r\n// 定义 ramda 变量\r\nconst R = require(\"ramda\");\r\n\r\n// 注册全局组件\r\n// 搜索 register.conf.js 文件，注册应用\r\nconst RegisterConfs = require.context(\"@components\", true, /\\/register\\.conf\\.js$/);\r\nRegisterConfs.keys().map((path) => {\r\n  const conf = RegisterConfs(path);\r\n  if (conf.global) {\r\n    const componentPath = path.replace(/^.\\/(.*)\\/register.conf.js$/, \"$1\");\r\n    vue.component(conf.name, (resolve) =>\r\n      require([`@components/${componentPath}/index.vue`], resolve)\r\n    );\r\n  }\r\n});\r\n\r\nexport default function addOn({ stores, routes }) {\r\n  window.math = math;\r\n  window.R = R;\r\n  // add-on routes\r\n  routes && router.addRoutes(routes);\r\n  // add-on store modules\r\n  stores &&\r\n    Object.keys(stores).map((name) => {\r\n      store.registerModule(name, stores[name]);\r\n    });\r\n\r\n  // 变成全局变量\r\n  vue.$bus = vue.prototype.$bus = new vue();\r\n  vue.$router = router;\r\n  vue.$store = store;\r\n\r\n  return {\r\n    store,\r\n    router,\r\n  };\r\n}\r\n", "import Vue from \"vue\";\r\n\r\n// eslint-disable-next-line no-unused-vars\r\nVue.filter(\"reviewStatus\", (value, fmt) => {\r\n  return R.path([\"latestStepHistory\", \"actionName\"], value) || \"草稿\";\r\n});\r\n\r\nVue.filter(\"storeType\", (value) => {\r\n  const map = {\r\n    1: \"门店\",\r\n    2: \"车队\",\r\n    3: \"工程机械\",\r\n  };\r\n  return map[value];\r\n});\r\n", "import vue from \"vue\";\r\nimport app from \"./app.vue\";\r\n\r\nimport stores from \"./stores.conf\";\r\nimport routes from \"./routes.conf\";\r\nimport addOn from \"@resources/add-on\";\r\n\r\nimport \"./resources/filter\";\r\n\r\nvue.config.productionTip = false;\r\n\r\nconst { store, router } = addOn({\r\n  stores,\r\n  routes,\r\n});\r\n\r\nnew vue({\r\n  store,\r\n  router,\r\n  render: (h) => h(app),\r\n}).$mount(\"#app\");\r\n", "import xhr from \"@utils/xhr\";\r\nimport dayjs from \"dayjs\";\r\nimport download from \"@utils/tools/download\";\r\n\r\nconst convertForm = (data) => {\r\n  let form = {\r\n    brand: data.brand,\r\n    signType: data.signType,\r\n    distributorId: data.distributorId,\r\n    distributorName: data.distributorName,\r\n    costCenter: data.costCenter,\r\n    companyCode: data.companyCode,\r\n    localMake: data.localMake,\r\n    retailerId: data.retailerId,\r\n    retailerName: data.retailerName,\r\n  };\r\n  if (data.salesChannel === \"signage\") {\r\n    form.partnerId = data.partnerId;\r\n    form.storeId = data.storeId;\r\n    form.storeName = data.storeName;\r\n    form.budgetAmount = data.budgetAmount;\r\n    form.settlementAmount = data.settlementAmount;\r\n    form.vatInvoiceType = data.vatInvoiceType;\r\n    if (data.supplierId) form.supplierId = data.supplierId;\r\n    // JSON 字符串\r\n    form.storeInfo = data.storeInfo;\r\n    form.storeSignInfo = data.storeSignInfo;\r\n    form.productInfo = data.productInfo;\r\n    form.applyAttFiles = data.applyAttFiles;\r\n  }else if (data.salesChannel === \"ck\") {\r\n    form.storeId = data.storeId;\r\n    form.storeName = data.storeName;\r\n    if (data.supplierId) form.supplierId = data.supplierId;\r\n    // JSON 字符串\r\n    // form.storeInfo = JSON.stringify(data.storeInfo);\r\n    // form.productInfo = JSON.stringify(data.productInfo);\r\n    form.applyAttFiles = data.applyAttFiles;\r\n  } else {\r\n    form.quoteAmount = data.quoteAmount;\r\n    // JSON 字符串\r\n    form.applyBaseInfo = data.applyBaseInfo;\r\n    form.applyAttFiles = data.applyAttFiles;\r\n    form.vatInvoiceType = data.vatInvoiceType;\r\n  }\r\n  if (data.id) form.id = data.id;\r\n  return form;\r\n};\r\n\r\nclass Service {\r\n  getApplyFormById(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.detail`,\r\n        params: [data.id, data.stepCode, data.executor],\r\n      },\r\n    });\r\n  }\r\n\r\n  getSignboardMaterial(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.querySignageMaterialType\",\r\n        params: [data.brand, data.applyType, data.localMake],\r\n      },\r\n    });\r\n  }\r\n\r\n  savaApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.save`,\r\n        params: [convertForm(data), data.executor],\r\n      },\r\n    });\r\n  }\r\n\r\n  operationApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.${\r\n          data.method\r\n        }`,\r\n        params: [convertForm(data), data.remark, data.executor, data.versionNo],\r\n      },\r\n    });\r\n  }\r\n\r\n  abortApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.${\r\n          data.method\r\n        }`,\r\n        params: [\r\n          {\r\n            id: data.id,\r\n          },\r\n        ],\r\n      },\r\n    });\r\n  }\r\n\r\n  abortRequest(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.${data.method}`,\r\n        params: [ { id: data.id }, 'ABORT', data.comment, data.executor, data.versionNo ],\r\n      },\r\n    });\r\n  }\r\n\r\n  recallRequest(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.${data.method}`,\r\n        params: [ { id: data.id }, data.comment, data.executor, data.versionNo ],\r\n      },\r\n    });\r\n  }\r\n\r\n  deleteApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: `mkt${data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}apply/delDraft.do`,\r\n      contentType: \"form\",\r\n      data: {\r\n        id: data.id,\r\n      },\r\n    });\r\n  }\r\n\r\n  getListPermission(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"operationPermissionService.getOperationPermissionByUser\",\r\n        params: [data.executor, data.moduleCode],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  getSeminarTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerSeminarTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getSignageTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerSignageTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getSignageStoreTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerStoreSignageTips\",\r\n        params: [data.partnerId, data.brand, data.storeId],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getOverallPerformanceTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerOverallPerformanceTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getCostCenter(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryDistributorCostCenter\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  getApplyList(data = {}) {\r\n    const startDate = data.dateRange\r\n      ? dayjs(data.dateRange[0]).format(\"YYYY-MM-DD 00:00:00\")\r\n      : \"\";\r\n    const endDate = data.dateRange ? dayjs(data.dateRange[1]).format(\"YYYY-MM-DD 23:59:59\") : \"\";\r\n\r\n    return xhr({\r\n      method: \"post\",\r\n      path: `mkt${data.salesChannel === \"signage\" ? \"signage\" :  data.salesChannel === \"ck\" ?\"ck\":\"seminar\"}apply/${data.method}.do`,\r\n      contentType: \"json\",\r\n      data: {\r\n        limit: data.limit,\r\n        start: (data.page - 1) * data.limit,\r\n        field: \"id\",\r\n        direction: \"DESC\",\r\n        distributorId: data.dealerId,\r\n        region: data.region,\r\n        brand: data.brand,\r\n        storeName: data.storeName,\r\n        signType: data.applyType,\r\n        startApplyTime: startDate,\r\n        endApplyTime: endDate,\r\n        executor: data.executor,\r\n      },\r\n    });\r\n  }\r\n\r\n  getReviewProcess(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${\r\n          data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"\r\n        }ApplyService.getWorkflowStepInstances`,\r\n        params: [data.id],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  getReviewHistory(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${\r\n          data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"\r\n        }ApplyService.getWorkflowStepHistory`,\r\n        params: [data.id],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  exportPDF(data = {}) {\r\n    if (data.type === \"ci\") {\r\n      return download({\r\n        path: \"/mktsignage/export/finalPDF.do\",\r\n        params: {\r\n          id: data.id,\r\n        },\r\n      });\r\n    } else if (data.type === \"cdm\") {\r\n      return download({\r\n        path: \"/mktseminar/export/finalPDF.do\",\r\n        params: {\r\n          id: data.id,\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  exportExcel(data = {}) {\r\n    return download({\r\n      path: \"/reportview/excel/export.do\",\r\n      data: {\r\n        packageName: data.packageName,\r\n        viewName: data.viewName,\r\n        fileName: data.fileName,\r\n        columnInfoDictKey: data.columnInfoDictKey,\r\n        pageType: data.pageType,\r\n      },\r\n    });\r\n  }\r\n\r\n  getFundDetail(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: `mkt/cio/getNewFundDetail.do`,\r\n      contentType: \"form\",\r\n      params: {\r\n        partnerId: data.dealerId,\r\n        mktType: data.applyType,\r\n        salesChannel: [\"1\"].indexOf(\"\" + data.brand) > -1 ? \"Consumer2021\" : \"Commercial2021\",\r\n        mktId: data.id,\r\n      },\r\n    });\r\n  }\r\n\r\n  getPartnerCkShopTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"mktCkApplyService.queryPartnerCkShopTips\",\r\n        params: [data.dealerId],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n}\r\n\r\nexport default new Service();\r\n", "import xhr from \"@utils/xhr\";\r\n\r\nclass Service {\r\n  getSuppliers(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: \"supplierService.getSuppliers\",\r\n        params: [data.partnerId, \"16\"],\r\n      },\r\n    });\r\n  }\r\n\r\n  getProductsSales(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerProductOverallPerformanceTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getDealersByKeyword(data = {}) {\r\n    if (data.urlPath) {\r\n      data.urlPath = data.urlPath.replace(/\\//g, \"-\");\r\n    } else {\r\n      data.urlPath = \"\";\r\n    }\r\n    return xhr({\r\n      method: \"get\",\r\n      path: \"partnerController/queryPartnerForCtrl.do\",\r\n      contentType: \"form\",\r\n      params: {\r\n        partnerName: data.partnerName,\r\n        resourceId: `resource-application${data.urlPath}`,\r\n      },\r\n    });\r\n  }\r\n\r\n  getStoreByDealerId(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"workshopmaster/mktApply/list.do\",\r\n      contentType: \"form\",\r\n      data: {\r\n        start: data.pageSize * (data.page - 1),\r\n        limit: data.pageSize,\r\n        queryType: 2,\r\n        workshopName: data.keyword,\r\n        partnerId: data.partnerId,\r\n        retailerId: data.retailerId,\r\n        status: 3,\r\n        brand: data.brand,\r\n        funFlag: data.funFlag,\r\n        resourceId: \"cdmMktApplySave\",\r\n        fromSource: \"3\",\r\n        businessWeight: \"2\",\r\n        mktKey: data.applyType,\r\n        oldMktKey: data.applyType,\r\n        pageIndex: 0,\r\n        field: \"id\",\r\n        direction: \"DESC\",\r\n      },\r\n    });\r\n  }\r\n}\r\n\r\nexport default new Service();\r\n", "export default {\r\n  id: \"\",\r\n  reqNo: \"\",\r\n  brand: \"\",\r\n  applyType: \"\",\r\n  dealerId: null,\r\n  dealerName: \"\",\r\n  includeDMS: 0,\r\n  partnerId: \"\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  // 客户信息\r\n  storeId: \"\",\r\n  storeCooperationyear: \"\",\r\n  storeName: \"\",\r\n  storeProvince: \"\",\r\n  storeCity: \"\",\r\n  storeRegion: \"\",\r\n  storeAddress: \"\",\r\n  storeContact: \"\",\r\n  storeContacts: \"\",\r\n  storeType: \"\",\r\n  storeCubicle: \"\",\r\n  storeWorkshopId: \"\",\r\n  storeCustomerType: \"\",\r\n  // 店招信息\r\n  localMake: \"\",\r\n  signboardMaterial: \"\",\r\n  signboardStyleFirst: \"\",\r\n  signboardStyleSecond: \"\",\r\n  signboardHeight: \"\",\r\n  signboardWidth: \"\",\r\n  signboardArea: \"\",\r\n  signboardQuote: \"\",\r\n  signboardDoorQuote: \"\",\r\n  signboardDecorationQuote: \"\",\r\n  signboardRequirement: \"\",\r\n  signboardSupplierId: \"\",\r\n  // 其他信息\r\n  otherApplyReason: \"\",\r\n  otherCompleteAmount: \"\",\r\n  otherSupplierConcact: \"\",\r\n  otherSupplierConcacts: \"\",\r\n  completeTime: \"\", // 完工时间\r\n  // 附件\r\n  attAppliedVehicle: [],\r\n  attCarQrcode: [],\r\n  attOriginalSignboard: [],\r\n  attOriginalOutdoorAdSign: [],\r\n  attQuotation: [],\r\n  attStampedQuotation: [],\r\n  attDesign: [],\r\n  attApplyForm: [],\r\n  attARIBAOrder: [],\r\n  attInvoice: [],\r\n  attConfirmProof: [],\r\n  attTripleAgreement: [],\r\n  attPaymentProof: [],\r\n  attCompletion: [],\r\n  attCarAdQuali: [],\r\n  attInvoiceConfirm: [],\r\n\r\n  // 权限\r\n  createTime: new Date(),\r\n  bizPermissionWeight: \"0\",\r\n  stepCode: \"\",\r\n  unitPriceLimit: \"\",\r\n  acceptAlias: \"\",\r\n  versionNo: \"\",\r\n  abortOperationName: \"\",\r\n  rejectOperationName: \"\",\r\n  acceptOperationName: \"\",\r\n  recallOperationName: \"\",\r\n  workflowInstance: {},\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n  //增值税发票\r\n  vatInvoiceType:'',\r\n  formStatus:'',\r\n\r\n};\r\n", "export default {\r\n  id: \"\",\r\n  reqNo: \"\",\r\n  brand: \"\",\r\n  applyType: \"SEMINAR\",\r\n  dealerId: null,\r\n  partnerId: \"\",\r\n  dealerName: \"\",\r\n  supplierId: \"\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  localMake: \"\",\r\n  // 研讨会信息\r\n  conferenceEstimatedDate: \"\",\r\n  conferenceAddress: \"\",\r\n  conferenceApplicationFee: \"\",\r\n  conferenceEstimatedPurchaseVolume: \"\",\r\n  conferenceNumberOfPeople: \"\",\r\n  conferencePlace: \"\",\r\n  conferenceQuote: \"\",\r\n  conferenceActualPurchaseVolume: \"\",\r\n  // 其他信息\r\n  otherApplyReason: \"\",\r\n  // 附件\r\n  attApplyForm: [], // 14A 申请表\r\n  attTripleAgreement: [], // 三方协议\r\n  attInvoice: [], // 发票\r\n  attInvoiceCheck: [], // 发票核验\r\n  attMeetingFlow: [], // 会议/活动流程或邀请函\r\n  attMeetingLocal: [], // 会议/活动现场照片\r\n  attMeetingSign: [], // 会议/活动参与人员签到表\r\n  attQuotation: [], // 费用明细单/报价单\r\n  attPaymentProof: [], // 经销商支付给第三方的付款证明\r\n  // 权限\r\n  createTime: new Date(),\r\n  bizPermissionWeight: \"0\",\r\n  stepCode: \"\",\r\n  acceptAlias: \"\",\r\n  versionNo: \"\",\r\n  abortOperationName: \"\",\r\n  rejectOperationName: \"\",\r\n  acceptOperationName: \"\",\r\n  recallOperationName: \"\",\r\n  quoteLimit: \"\", // 预算限额\r\n  unitPriceLimit: \"\", // 平均价格限额\r\n  workflowInstance: {},\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n  //增值税发票\r\n  vatInvoiceType:'',\r\n  formStatus:'',\r\n};\r\n", "export default {\r\n  id: \"\",\r\n  reqNo: \"\",\r\n  organizationName:\"\",\r\n  brand: \"\",\r\n  applyType: \"\",\r\n  dealerId: null,\r\n  dealerName: \"\",\r\n  includeDMS: 0,\r\n  partnerId: \"\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  // 客户信息\r\n  storeId: \"\",\r\n  storeCooperationyear: \"\",\r\n  storeName: \"\",\r\n  storeProvince: \"\",\r\n  storeCity: \"\",\r\n  storeRegion: \"\",\r\n  storeAddress: \"\",\r\n  storeContact: \"\",\r\n  storeContacts: \"\",\r\n  storeType: \"\",\r\n  storeCubicle: \"\",\r\n  storeWorkshopId: \"\",\r\n  storeCustomerType: \"\",\r\n  // 店招信息\r\n  localMake: \"\",\r\n  signboardStyleSecond: \"\",\r\n  signboardHeight: \"\",\r\n  signboardWidth: \"\",\r\n  signboardArea: \"\",\r\n  signboardQuote: \"\",\r\n  signboardDoorQuote: \"\",\r\n  signboardDecorationQuote: \"\",\r\n  signboardRequirement: \"\",\r\n  signboardSupplierId: \"\",\r\n  // 其他信息\r\n  otherApplyReason: \"\",\r\n  otherCompleteAmount: \"\",\r\n  otherSupplierConcact: \"\",\r\n  otherSupplierConcacts: \"\",\r\n  completeTime: \"\", // 完工时间\r\n  // 附件\r\n  attQuotation: [],\r\n  attOriginalSignboard:[],\r\n  // 权限\r\n  createTime: new Date(),\r\n  bizPermissionWeight: \"0\",\r\n  stepCode: \"\",\r\n  unitPriceLimit: \"\",\r\n  acceptAlias: \"\",\r\n  versionNo: \"\",\r\n  abortOperationName: \"\",\r\n  rejectOperationName: \"\",\r\n  acceptOperationName: \"\",\r\n  recallOperationName: \"\",\r\n  workflowInstance: {},\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n  oldData:{},\r\n  storeInfo:{},\r\n  applyAttFiles:{}\r\n};\r\n", "// 赋值的属性，会取对应名字的值\r\nexport default {\r\n  id: \"\",\r\n  brand: \"\",\r\n  signType: \"applyType\",\r\n  distributorId: \"dealerId\",\r\n  distributorName: \"dealerName\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  storeId: \"\",\r\n  storeName: \"\",\r\n  localMake: \"\",\r\n  customerType: \"storeCustomerType\",\r\n  budgetAmount: \"signboardQuote\",\r\n  supplierId: \"signboardSupplierId\",\r\n  settlementAmount: \"otherCompleteAmount\",\r\n  completeTime: \"\",\r\n  productInfo: [],\r\n  storeInfo: {},\r\n  storeSignInfo: {},\r\n  applyAttFiles: {},\r\n\r\n  // 表单以外的字段\r\n  method: \"\",\r\n  remark: \"\",\r\n  executor: \"\",\r\n  salesChannel: \"\",\r\n  versionNo: \"\",\r\n\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n\r\n  //增值税发票\r\n  vatInvoiceType:'',\r\n};\r\n", "// 赋值的属性，会取对应名字的值\r\nexport default {\r\n  id: \"\",\r\n  brand: \"\",\r\n  signType: \"applyType\",\r\n  distributorId: \"dealerId\",\r\n  distributorName: \"dealerName\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  quoteAmount: \"conferenceQuote\",\r\n  localMake: \"\",\r\n  applyBaseInfo: {},\r\n  applyAttFiles: {},\r\n  // 表单以外的字段\r\n  method: \"\",\r\n  remark: \"\",\r\n  executor: \"\",\r\n  salesChannel: \"\",\r\n  versionNo: \"\",\r\n\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n\r\n  //增值税发票\r\n  vatInvoiceType:'',\r\n};\r\n", "// 赋值的属性，会取对应名字的值\r\nexport default {\r\n  id: \"\",\r\n  brand: \"\",\r\n  signType: \"applyType\",\r\n  distributorId: \"dealerId\",\r\n  distributorName: \"dealerName\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  storeId: \"\",\r\n  storeName: \"\",\r\n  localMake: \"\",\r\n  customerType: \"storeCustomerType\",\r\n  budgetAmount: \"signboardQuote\",\r\n  supplierId: \"signboardSupplierId\",\r\n  settlementAmount: \"otherCompleteAmount\",\r\n  completeTime: \"\",\r\n  productInfo: [],\r\n  storeInfo: {},\r\n  storeSignInfo: {},\r\n  applyAttFiles: {},\r\n\r\n  // 表单以外的字段\r\n  method: \"\",\r\n  remark: \"\",\r\n  executor: \"\",\r\n  salesChannel: \"\",\r\n  versionNo: \"\",\r\n\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n};\r\n", "export default (source, target) => {\r\n  source.organizationName=target.organizationName\r\n  source.versionNo = target.workflowInstance.versionNo;\r\n  if(target.applyAttFiles){\r\n    target.applyAttFiles=JSON.parse(target.applyAttFiles)\r\n  }\r\n\r\n  for (let name in source) {\r\n    if (name === \"versionNo\") {\r\n      source.versionNo = target.workflowInstance.versionNo;\r\n    }else if (name === \"applyType\") {\r\n      source.applyType = target.signType || \"STORE_SIGN\";\r\n    } else if (name === \"dealerId\") {\r\n      source.dealerId = target.distributorId;\r\n    } else if (name === \"dealerName\") {\r\n      source.dealerName = target.distributorName;\r\n    }else if (/^att.*$/.test(name)) {\r\n      source[name]=[]\r\n      if(target.applyAttFiles){\r\n        source[name]=target.applyAttFiles[name]\r\n      }\r\n    } else if ([\"quoteLimit\", \"unitPriceLimit\",\"storeInfo\"].indexOf(name) > -1) {\r\n      continue;\r\n    } else {\r\n      // 其他情况\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n  if(!source.attQuotation){\r\n    source.attQuotation=[]\r\n  }\r\n  source.oldData=target\r\n  source.oldData.storeInfo = JSON.parse(target.storeInfo);\r\n  source.oldData.productInfo = JSON.parse(target.productInfo);\r\n  return source;\r\n};\r\n", "export default (target = []) => {\r\n  return target.map(item => ({\r\n    name: item.name,\r\n    attId: item.attId,\r\n    fileType: item.fileType,\r\n    sourceType: item.sourceType,\r\n    storageName: item.storageName,\r\n    storePath: item.storePath,\r\n    remoteUrl: item.remoteUrl,\r\n  }))\r\n}", "import toRequestAttFormat from \"./to-request-att\";\r\n\r\nexport default (source, target) => {\r\n  let name = \"\";\r\n\r\n  // 懒得在界面去算了，在返回给后端的时候算一次发过去，前端不需要这个值\r\n  // if (target.signboardHeight && target.signboardWidth) {\r\n  //   target.signboardArea = Math.round(target.signboardHeight && target.signboardWidth * 100) / 100;\r\n  // }\r\n\r\n  source.applyAttFiles={}\r\n  for (name in source) {\r\n    if (source[name] in target) {\r\n      source[name] = target[source[name]];\r\n    } else if (target[name]) {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n\r\n\r\n  for (name in target) {\r\n    if (name === \"id\") {\r\n      // 去掉空 id\r\n      if (target.id) {\r\n        source.id = target.id;\r\n      } else {\r\n        delete source.id;\r\n      }\r\n    }else if (/^store.*$/.test(name)) {\r\n      // storeInfo\r\n      source.storeInfo[name] = target[name];\r\n    } else if (/^signboard.*$/.test(name)) {\r\n      // storeSignInfo\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (/^other.*$/.test(name)) {\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (name === \"products\") {\r\n      // productInfo\r\n      source.productInfo = target.products;\r\n    } else if (/^att.*$/.test(name)) {\r\n      source.applyAttFiles[name] = toRequestAttFormat(target[name]);\r\n    }\r\n  }\r\n  source.applyAttFiles = JSON.stringify(source.applyAttFiles);\r\n\r\n  return source;\r\n};\r\n", "export default (source, target) => {\r\n  target.storeInfo = JSON.parse(target.storeInfo);\r\n  target.storeSignInfo = JSON.parse(target.storeSignInfo);\r\n  target.productInfo = JSON.parse(target.productInfo);\r\n  target.applyAttFiles = JSON.parse(target.applyAttFiles);\r\n\r\n  for (let name in source) {\r\n    if (name === \"versionNo\") {\r\n      source.versionNo = target.workflowInstance.versionNo;\r\n    } else if (name === \"partnerId\") {\r\n      source[name] = target.storeSignInfo[name];\r\n    } else if (name === \"includeDMS\") {\r\n      source[name] = target.storeSignInfo[name];\r\n    } else if (name === \"applyType\") {\r\n      source.applyType = target.signType || \"STORE_SIGN\";\r\n    } else if (name === \"dealerId\") {\r\n      source.dealerId = target.distributorId;\r\n    } else if (name === \"dealerName\") {\r\n      source.dealerName = target.distributorName;\r\n    } else if (/^store.*$/.test(name)) {\r\n      // storeInfo\r\n      source[name] = target.storeInfo[name];\r\n    } else if (\r\n      // storeSignInfo\r\n      /^signboard.*$/.test(name) ||\r\n      /^other.*$/.test(name)\r\n    ) {\r\n      source[name] = target.storeSignInfo[name];\r\n    } else if (/^att.*$/.test(name)) {\r\n      source[name] = target.applyAttFiles[name] || [];\r\n    } else if ([\"quoteLimit\", \"unitPriceLimit\"].indexOf(name) > -1) {\r\n      continue;\r\n    } else {\r\n      // 其他情况\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n  return source;\r\n};\r\n", "import toRequestAttFormat from \"./to-request-att\";\r\n\r\nexport default (source, target) => {\r\n  let name = \"\";\r\n\r\n  // 懒得在界面去算了，在返回给后端的时候算一次发过去，前端不需要这个值\r\n  // if (target.signboardHeight && target.signboardWidth) {\r\n  //   target.signboardArea = Math.round(target.signboardHeight && target.signboardWidth * 100) / 100;\r\n  // }\r\n\r\n  for (name in source) {\r\n    if (source[name] in target) {\r\n      source[name] = target[source[name]];\r\n    } else if (target[name]) {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n\r\n  for (name in target) {\r\n    if (name === \"id\") {\r\n      // 去掉空 id\r\n      if (target.id) {\r\n        source.id = target.id;\r\n      } else {\r\n        delete source.id;\r\n      }\r\n    } else if (name === \"partnerId\") {\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (/^store.*$/.test(name)) {\r\n      // storeInfo\r\n      source.storeInfo[name] = target[name];\r\n    } else if (/^signboard.*$/.test(name)) {\r\n      // storeSignInfo\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (/^other.*$/.test(name)) {\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (name === \"products\") {\r\n      // productInfo\r\n      source.productInfo = target.products;\r\n    } else if (/^att.*$/.test(name)) {\r\n      source.applyAttFiles[name] = toRequestAttFormat(target[name]);\r\n    }\r\n  }\r\n\r\n  source.storeInfo = JSON.stringify(source.storeInfo);\r\n  source.storeSignInfo = JSON.stringify(source.storeSignInfo);\r\n  source.productInfo = JSON.stringify(source.productInfo);\r\n  source.applyAttFiles = JSON.stringify(source.applyAttFiles);\r\n\r\n  return source;\r\n};\r\n", "export default (source, target) => {\r\n  target.salesInfo = JSON.parse(target.salesInfo || \"[]\");\r\n  target.applyBaseInfo = JSON.parse(target.applyBaseInfo || \"{}\");\r\n  target.applyAttFiles = JSON.parse(target.applyAttFiles || \"{}\");\r\n\r\n  for (let name in source) {\r\n    if (name === \"versionNo\") {\r\n      source.versionNo = target.workflowInstance.versionNo;\r\n    } else if (name === \"partnerId\") {\r\n      source[name] = target.applyBaseInfo[name];\r\n    } else if (name === \"applyType\") {\r\n      source.applyType = target.signType || \"STORE_SIGN\";\r\n    } else if (name === \"dealerId\") {\r\n      source.dealerId = target.distributorId;\r\n    } else if (name === \"dealerName\") {\r\n      source.dealerName = target.distributorName;\r\n    } else if (\r\n      /^signboard.*$/.test(name) ||\r\n      /^conference.*$/.test(name) ||\r\n      /^store.*$/.test(name) ||\r\n      /^other.*$/.test(name)\r\n    ) {\r\n      // applyBaseInfo\r\n      source[name] = target.applyBaseInfo[name];\r\n    } else if (/^att.*$/.test(name)) {\r\n      source[name] = target.applyAttFiles[name] || [];\r\n    }\r\n    // 其他情况\r\n    else {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n  return source;\r\n};\r\n", "import toRequestAttFormat from \"./to-request-att\";\r\n\r\nexport default (source, target) => {\r\n  let name = \"\";\r\n\r\n  // 懒得在界面去算了，在返回给后端的时候算一次发过去，前端不需要这个值\r\n  if (target.signboardHeight && target.signboardWidth) {\r\n    target.signboardArea = Math.round(target.signboardHeight && target.signboardWidth * 100) / 100;\r\n  }\r\n\r\n  for (name in source) {\r\n    if (source[name] in target) {\r\n      source[name] = target[source[name]];\r\n    } else if (target[name]) {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n\r\n  for (name in target) {\r\n    if (name === \"id\") {\r\n      // 去掉空 id\r\n      if (target.id) {\r\n        source.id = target.id;\r\n      } else {\r\n        delete source.id;\r\n      }\r\n    } else if (name === \"partnerId\") {\r\n      source.applyBaseInfo[name] = target[name];\r\n    } else if (/^conference.*$/.test(name) || /^other.*$/.test(name)) {\r\n      // applyBaseInfo\r\n      if (target[name] !== undefined || target[name] !== \"\") {\r\n        source.applyBaseInfo[name] = target[name];\r\n      }\r\n    } else if (/^att.*$/.test(name)) {\r\n      source.applyAttFiles[name] = toRequestAttFormat(target[name]);\r\n    }\r\n  }\r\n\r\n  source.applyBaseInfo = JSON.stringify(source.applyBaseInfo);\r\n  source.applyAttFiles = JSON.stringify(source.applyAttFiles);\r\n\r\n  return source;\r\n};\r\n", "import LocalSignage from \"./_values/local-signage\";\r\nimport LocalSeminar from \"./_values/local-seminar\";\r\nimport LocalCk from \"./_values/local-ck\";\r\nimport RequestSignage from \"./_values/request-signage\";\r\nimport RequestSeminar from \"./_values/request-seminar\";\r\nimport RequestCk from \"./_values/request-ck\";\r\nimport applyService from \"@projects/market/resource-application-2021/resources/service/apply\";\r\nimport toCkLocal from \"./_func/to-ck-local\";\r\nimport toCkRequest from \"./_func/to-ck-request\";\r\nimport toSignageLocal from \"./_func/to-signage-local\";\r\nimport toSignageRequest from \"./_func/to-signage-request\";\r\nimport toSeminarLocal from \"./_func/to-seminar-local\";\r\nimport toSeminarRequest from \"./_func/to-seminar-request\";\r\nimport vue from \"vue\";\r\n// eslint-disable-next-line no-unused-vars\r\nimport De from \"element-ui/src/locale/lang/de\";\r\n\r\nconst state = {\r\n  signage: Object.assign({}, LocalSignage),\r\n  seminar: Object.assign({}, LocalSeminar),\r\n  ck: Object.assign({}, LocalCk),\r\n};\r\n\r\nconst getters = {\r\n  applyForm(state, getters) {\r\n    return state[getters.salesChannel];\r\n  },\r\n  hasAuthInBiz(state, getters) {\r\n    return (payload) => {\r\n      payload = math.log(payload, 2) + 1;\r\n\r\n      let weight = state[getters.salesChannel].bizPermissionWeight;\r\n      let bit = 0;\r\n      let result = false;\r\n      if (payload > 32) {\r\n        if (weight >= 4294967296) {\r\n          weight = parseInt(weight / 4294967296);\r\n          payload = payload - 32;\r\n          bit = weight >>> (payload - 1);\r\n        } else {\r\n          bit = 0;\r\n        }\r\n      } else {\r\n        bit = weight >>> (payload - 1);\r\n      }\r\n\r\n      result = !!(payload && !isNaN(payload - 1) && !!(bit & 1));\r\n      return result;\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  CLEAR_APPLY_FORM(state, payload) {\r\n    if (payload.salesChannel === \"signage\") {\r\n      state.signage = R.clone(LocalSignage);\r\n    }else if (payload.salesChannel === \"ck\") {\r\n      state.ck = R.clone(LocalCk);\r\n    } else {\r\n      state.seminar = R.clone(LocalSeminar);\r\n    }\r\n  },\r\n  SET_STORE_INFO(state, payload) {\r\n    state.signage.storeId = payload.id || \"\";\r\n    state.signage.storeName = payload.workshopName || \"\";\r\n    state.signage.storeAddress = payload.workshopAddress || \"\";\r\n    state.signage.storeContacts = payload.contactPerson || \"\";\r\n    state.signage.storeContact = payload.contactPersonTel || \"\";\r\n    state.signage.storeWorkshopId = payload.id;\r\n    state.signage.storeProvince = payload.provinceName || \"\";\r\n    state.signage.storeCity = payload.cityName || \"\";\r\n    state.signage.storeType = payload.type || \"\";\r\n    state.signage.storeCubicle = payload.seatsNum || \"\";\r\n    state.signage.storeRegion = payload.regionName;\r\n    state.signage.storeCustomerType = payload.customerType;\r\n  },SET_CK_STORE_INFO(state, payload) {\r\n    state.ck.storeId = payload.id || \"\";\r\n    state.ck.storeName = payload.workshopName || \"\";\r\n    state.ck.storeAddress = payload.workshopAddress || \"\";\r\n    state.ck.storeContacts = payload.contactPerson || \"\";\r\n    state.ck.storeContact = payload.contactPersonTel || \"\";\r\n    state.ck.storeWorkshopId = payload.id;\r\n    state.ck.storeProvince = payload.provinceName || \"\";\r\n    state.ck.storeCity = payload.cityName || \"\";\r\n    state.ck.storeType = payload.type || \"\";\r\n    state.ck.storeCubicle = payload.seatsNum || \"\";\r\n    state.ck.storeRegion = payload.regionName;\r\n    state.ck.storeCustomerType = payload.customerType;\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getApplyFormById({ getters, dispatch }, payload) {\r\n    const [status, res] = await applyService.getApplyFormById({\r\n      id: payload.id,\r\n      stepCode: payload.stepCode,\r\n      executor: getters.executor,\r\n      salesChannel: getters.salesChannel,\r\n    });\r\n    if (status) {\r\n      if (getters.salesChannel === \"signage\") {\r\n        toSignageLocal(getters.applyForm, R.merge(res.result.form, res.result.currentStep));\r\n        // 更新产品信息\r\n        dispatch(\"initProducts\", JSON.parse(res.result.form.productInfo));\r\n      }else if (getters.salesChannel === \"ck\") {\r\n        toCkLocal(getters.applyForm, R.merge(res.result.form, res.result.currentStep));\r\n        // 更新产品信息\r\n        dispatch(\"initCkProducts\", JSON.parse(res.result.form.productInfo));\r\n      } else {\r\n        toSeminarLocal(getters.applyForm, R.merge(res.result.form, res.result.currentStep));\r\n      }\r\n    }\r\n    return [status, res];\r\n  },\r\n  async savaApplyForm({ getters }) {\r\n    let target = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      getters.applyForm\r\n    );\r\n\r\n    if (getters.salesChannel === \"signage\") {\r\n      target.products = R.clone(getters.products);\r\n    }else if (getters.salesChannel === \"ck\") {\r\n      target.products = R.clone(getters.products);\r\n    } else {\r\n      target.sales = R.clone(getters.sales);\r\n    }\r\n\r\n    const params =\r\n      getters.salesChannel === \"signage\"\r\n        ? toSignageRequest(R.clone(RequestSignage), target)\r\n        : getters.salesChannel === \"ck\" ? toCkRequest(R.clone(RequestCk), target)\r\n          : toSeminarRequest(R.clone(RequestSeminar), target);\r\n\r\n    const [status, res] = await applyService.savaApplyForm(params);\r\n    if (status) {\r\n      getters.applyForm.id = res.result.id;\r\n    }\r\n    return [status, res];\r\n  },\r\n  async operationApplyForm({ getters }, payload) {\r\n    let target = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      R.merge(getters.applyForm, payload)\r\n    );\r\n\r\n    if (getters.salesChannel === \"signage\") {\r\n      target.products = R.clone(getters.products);\r\n    }else if (getters.salesChannel === \"ck\") {\r\n      target.products = R.clone(getters.ckProducts);\r\n    }  else {\r\n      target.sales = R.clone(getters.sales);\r\n    }\r\n\r\n    const params =\r\n      getters.salesChannel === \"signage\"\r\n        ? toSignageRequest(R.clone(RequestSignage), target)\r\n        : getters.salesChannel === \"ck\"\r\n          ? toCkRequest(R.clone(RequestCk), target)\r\n          :toSeminarRequest(R.clone(RequestSeminar), target);\r\n    const [status, res] = await applyService.operationApplyForm(params);\r\n    return [status, res];\r\n  },\r\n  async abortApplyForm({ getters, rootState }, payload) {\r\n    let params = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.abortApplyForm(params);\r\n    if (status) {\r\n      rootState.list.observer.data.find((item, index) => {\r\n        if (item.id === payload.id) {\r\n          vue.set(rootState.list.observer.data[index], \"formStatus\", 5);\r\n        }\r\n      });\r\n    }\r\n    return [status, res];\r\n  },\r\n  async abortByStep({ getters }, payload) {\r\n    let params = R.merge(\r\n      { executor: getters.executor, salesChannel: getters.salesChannel },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.abortRequest(params);\r\n    return [status, res];\r\n  },\r\n  async recallByRequest({ getters }, payload) {\r\n    let params = R.merge(\r\n      { executor: getters.executor, salesChannel: getters.salesChannel },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.recallRequest(params);\r\n    return [status, res];\r\n  },\r\n  async deleteApplyForm({ getters, rootState }, payload) {\r\n    let params = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.deleteApplyForm(params);\r\n    if (status) {\r\n      rootState.list.todo.data = rootState.list.todo.data.filter((item) => item.id !== payload.id);\r\n    }\r\n    return [status, res];\r\n  },\r\n  // eslint-disable-next-line no-empty-pattern\r\n  async exportPDF({}, payload) {\r\n    const [status, res] = await applyService.exportPDF(payload);\r\n    return [status, res];\r\n  },\r\n  exportExcel({ getters }, payload) {\r\n    const params = R.merge(\r\n      {\r\n        packageName: getters.salesChannel === \"signage\" ? \"signageapply2021\" : \"seminarapply2021\",\r\n      },\r\n      payload\r\n    );\r\n\r\n    applyService.exportExcel(params);\r\n  },\r\n  async getFundDetail({ getters }, payload) {\r\n    const params = R.merge(\r\n      {\r\n        brand: getters.applyForm.brand,\r\n        dealerId: getters.applyForm.dealerId,\r\n        applyType: getters.applyForm.applyType,\r\n        salesChannel: getters.salesChannel,\r\n        id: getters.applyForm.id,\r\n      },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.getFundDetail(params);\r\n    return [status, res];\r\n  },\r\n  async getCostCenter({ getters }) {\r\n    const [status, res] = await applyService.getCostCenter(getters.applyForm);\r\n    if (status) {\r\n      getters.applyForm.costCenter = res.result.data.costCenter;\r\n      getters.applyForm.companyCode = res.result.data.companyCode;\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "const state = {\r\n  tableData: [],\r\n};\r\n\r\nconst getters = {\r\n  ckProducts(state) {\r\n    return state.tableData;\r\n  },\r\n};\r\n\r\nconst mutations = {};\r\n\r\nconst actions = {\r\n  // eslint-disable-next-line no-unused-vars\r\n  initCkProducts({ state, getters, dispatch }, payload) {\r\n    state.tableData=payload\r\n  }\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import commonService from \"@projects/market/resource-application-2021/resources/service/common\";\r\n\r\nconst state = {\r\n  store: {\r\n    title: \"选择客户\",\r\n    show: false,\r\n    params: {},\r\n    list: {\r\n      total: 0,\r\n      loading: false,\r\n      loadingText: \"正在加载数据\",\r\n      data: [],\r\n      page: 1,\r\n      pageSize: 10,\r\n    },\r\n  },\r\n  signboardStyle: {\r\n    title: \"店招样式展示\",\r\n    show: false,\r\n    params: {},\r\n  },\r\n  appliedVehicle: {\r\n    title: \"申请车辆照片展示\",\r\n    show: false,\r\n    params: {},\r\n  },\r\n};\r\n\r\nconst getters = {};\r\n\r\nconst mutations = {\r\n  SHOW_DIALOG(state, payload) {\r\n    let name = payload.dialogName;\r\n\r\n    if (!name) return false;\r\n\r\n    state[name].show = true;\r\n    state[name].title = payload.title || state[name].title;\r\n    state[name].params = payload.params;\r\n\r\n    if (state[name].list) {\r\n      state[name].list.page = 1;\r\n      state[name].list.total = 0;\r\n    }\r\n  },\r\n  CLEAR_STORE_LIST(state) {\r\n    state.store.list.total = 0;\r\n    state.store.list.page = 1;\r\n  },\r\n  HIDE_DIALOG(state, payload) {\r\n    let name = payload.dialogName;\r\n\r\n    if (!name) return false;\r\n\r\n    state[name].show = false;\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getStoreByDealerId({ state, getters }, payload) {\r\n    let params = {};\r\n\r\n    if (state.store.list.loading) return false;\r\n\r\n    state.store.list.data = [];\r\n\r\n    params = R.merge(\r\n      {\r\n        page: state.store.list.page,\r\n        pageSize: state.store.list.pageSize,\r\n        applyType: getters.applyForm.applyType,\r\n        funFlag: \"mktCi\",\r\n        salesChannel: getters.salesChannel,\r\n        partnerId: getters.applyForm.retailerId || getters.applyForm.partnerId,\r\n        brand: getters.applyForm.brand,\r\n      },\r\n      payload\r\n    );\r\n\r\n    state.store.list.loading = true;\r\n    const [status, res] = await commonService.getStoreByDealerId(params);\r\n    state.store.list.loading = false;\r\n\r\n    if (status) {\r\n      state.store.list.data = res.resultLst;\r\n      state.store.list.total = res.total;\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "// eslint-disable-next-line no-unused-vars\r\nimport commonService from \"@projects/market/resource-application-2021/resources/service/common\";\r\n\r\nconst state = {\r\n  salesChannel: \"\",\r\n  executor: \"\",\r\n  previewForm: false, // 是否是预览表单\r\n};\r\n\r\nconst getters = {\r\n  salesChannel(state) {\r\n    return state.salesChannel;\r\n  },\r\n  executor(state) {\r\n    return state.executor;\r\n  },\r\n  previewForm(state) {\r\n    return state.previewForm;\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  SET_GLOBAL(state, payload) {\r\n    for (let name in payload) {\r\n      state[name] = payload[name];\r\n    }\r\n  },\r\n};\r\n\r\nconst actions = {};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import applyService from \"@projects/market/resource-application-2021/resources/service/apply\";\r\n\r\nconst state = {\r\n  done: {\r\n    searchParams: {\r\n      brand: \"\",\r\n      region: \"\",\r\n      storeName: \"\",\r\n      dealerId: \"\",\r\n      applyType: \"\",\r\n      dateRange: \"\",\r\n    },\r\n    pageParams: {\r\n      limit: 10,\r\n      page: 1,\r\n      total: 0,\r\n    },\r\n    status: {\r\n      loading: false,\r\n    },\r\n    data: [],\r\n  },\r\n  observer: {\r\n    searchParams: {\r\n      brand: \"\",\r\n      region: \"\",\r\n      storeName: \"\",\r\n      dealerId: \"\",\r\n      applyType: \"\",\r\n      dateRange: \"\",\r\n    },\r\n    pageParams: {\r\n      limit: 10,\r\n      page: 1,\r\n      total: 0,\r\n    },\r\n    status: {\r\n      loading: false,\r\n    },\r\n    data: [],\r\n  },\r\n  permissionWeight: 0,\r\n};\r\n\r\nconst getters = {\r\n  hasAuthInList(state) {\r\n    return (payload) => {\r\n      return !!(\r\n        payload &&\r\n        !isNaN(payload - 1) &&\r\n        !!((state.permissionWeight >> (payload - 1)) & 1)\r\n      );\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {};\r\n\r\nconst actions = {\r\n  async getListByDone({ state, getters }) {\r\n    if (state.done.status.loading) return false;\r\n    const params = R.merge(\r\n      {\r\n        method: \"donedata\",\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      R.merge(state.done.searchParams, state.done.pageParams)\r\n    );\r\n\r\n    state.done.status.loading = true;\r\n    const [status, res] = await applyService.getApplyList(params);\r\n    state.done.status.loading = false;\r\n\r\n    if (status) {\r\n      state.done.data = res.resultLst;\r\n      state.done.pageParams.total = res.total;\r\n    }\r\n    return [status, res];\r\n  },\r\n  async getListByObserver({ state, getters }) {\r\n    if (state.observer.status.loading) return false;\r\n    const params = R.merge(\r\n      {\r\n        method: \"alldata\",\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      R.merge(state.observer.searchParams, state.observer.pageParams)\r\n    );\r\n\r\n    state.observer.status.loading = true;\r\n    const [status, res] = await applyService.getApplyList(params);\r\n    state.observer.status.loading = false;\r\n\r\n    if (status) {\r\n      state.observer.data = res.resultLst;\r\n      state.observer.pageParams.total = res.total;\r\n    }\r\n    return [status, res];\r\n  },\r\n  async getListPermission({ state, getters }) {\r\n    const params = {\r\n      executor: getters.executor,\r\n      moduleCode: `${getters.salesChannel === \"signage\" ? \"Signage\" : getters.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}.apply`,\r\n    };\r\n    const [status, res] = await applyService.getListPermission(params);\r\n    if (status) {\r\n      state.permissionWeight = res.result.weight;\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "export default [\r\n  {\r\n    categoryName: \"全合成\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    categoryName: \"合成型\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    categoryName: \"其他\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    category: \"Total\",\r\n    categoryName: \"总计\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n];\r\n", "export default [\r\n  {\r\n    categoryName: \"德乐400以上\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    categoryName: \"其他\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    category: \"Total\",\r\n    categoryName: \"总计\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n];\r\n", "import commonService from \"@projects/market/resource-application-2021/resources/service/common\";\r\nimport packageProducts from \"./_values/package\";\r\nimport deloProducts from \"./_values/delo\";\r\nimport vue from \"vue\";\r\n\r\nconst state = {\r\n  tableData: [],\r\n};\r\n\r\nconst getters = {\r\n  products(state) {\r\n    return state.tableData;\r\n  },\r\n};\r\n\r\nconst mutations = {};\r\n\r\nconst actions = {\r\n  initProducts({ state, getters, dispatch }, payload) {\r\n    if (payload) {\r\n      payload.map((item, index) => {\r\n        vue.set(state.tableData, index, item);\r\n      });\r\n    } else {\r\n      if ([\"1\"].indexOf(\"\" + getters.applyForm.brand) > -1) {\r\n        if (!state.tableData[0] || state.tableData[0].categoryName !== \"全合成\")\r\n          state.tableData = R.clone(packageProducts);\r\n      } else {\r\n        if (!state.tableData[0] || state.tableData[0].categoryName !== \"德乐 400 以上\")\r\n          state.tableData = R.clone(deloProducts);\r\n      }\r\n    }\r\n    dispatch(\"getProductsSales\");\r\n  },\r\n  async getProductsSales({ state, getters }) {\r\n    const [status, res] = await commonService.getProductsSales(getters.applyForm);\r\n    if (status) {\r\n      let totalActualPack = 0;\r\n      state.tableData.map((tableItem) => {\r\n        res.result.data.map((dataItem) => {\r\n          if (tableItem.categoryName === dataItem.productCategory) {\r\n            tableItem.actualPack = dataItem.rolling12MonthSellIn;\r\n            totalActualPack = math\r\n              .add(math.bignumber(totalActualPack), math.bignumber(tableItem.actualPack))\r\n              .valueOf();\r\n          }\r\n        });\r\n        if (tableItem.categoryName === \"总计\") {\r\n          tableItem.actualPack = totalActualPack;\r\n        }\r\n      });\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "module.exports = [\r\n  {\r\n    path: \"/signage/apply/:id?\",\r\n    component: (resolve) => require([\"./views/apply/signage\"], resolve),\r\n    meta: {\r\n      title: \"Signage Application Form\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/seminar/apply/:id?\",\r\n    component: (resolve) => require([\"./views/apply/seminar\"], resolve),\r\n    meta: {\r\n      title: \"Seminar Application Form\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/signage/list\",\r\n    component: (resolve) => require([\"./views/list/signage\"], resolve),\r\n    meta: {\r\n      title: \"Signage Application List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/seminar/list\",\r\n    component: (resolve) => require([\"./views/list/seminar\"], resolve),\r\n    meta: {\r\n      title: \"Seminar Application List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/signage/observer\",\r\n    component: (resolve) => require([\"./views/list/signage/observer\"], resolve),\r\n    meta: {\r\n      title: \"Signage Application Observer List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/seminar/observer\",\r\n    component: (resolve) => require([\"./views/list/seminar/observer\"], resolve),\r\n    meta: {\r\n      title: \"Seminar Application Observer List\",\r\n    },\r\n  },\r\n\r\n  {\r\n    path: \"/ck/observer\",\r\n    component: (resolve) => require([\"./views/list/ck/observer\"], resolve),\r\n    meta: {\r\n      title: \"CK Application Observer List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/ck/apply/:id?\",\r\n    component: (resolve) => require([\"./views/apply/ck\"], resolve),\r\n    meta: {\r\n      title: \"CK Application Form\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/ck/list\",\r\n    component: (resolve) => require([\"./views/list/ck\"], resolve),\r\n    meta: {\r\n      title: \"CK Application List\",\r\n    },\r\n  },\r\n];\r\n", "module.exports = {\r\n  apply: require(\"./resources/storeModules/apply\").default,\r\n  list: require(\"./resources/storeModules/list\").default,\r\n  dialog: require(\"./resources/storeModules/dialog\").default,\r\n  global: require(\"./resources/storeModules/global\").default,\r\n  products: require(\"./resources/storeModules/products\").default,\r\n  ckProducts: require(\"./resources/storeModules/ck_products\").default,\r\n};\r\n", "import notify from '@utils/dialog/notify'\r\n\r\nexport const Timeout = 20000\r\n\r\nexport const GoLogin = async () => {\r\n  if (process.env.NODE_ENV === 'development') {\r\n    const projectName = window.location.pathname.replace(/\\/([^/]*).*/, '$1')\r\n    if (projectName !== 'dev') {\r\n      return window.location.href = '/dev#/'\r\n    }\r\n  } else {\r\n    return top && (top.location = '/logout.do')\r\n  }\r\n}\r\n\r\nexport const StatusErrorHandler = async (e) => {\r\n  const data = e.data\r\n  if (!e.data) {\r\n    // do nothing\r\n    if (e.message === 'Request failed with status code 502') {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '服务器响应失败，请联系管理人员！'\r\n      })\r\n    } else {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n      })\r\n    }\r\n  } else if (data.code === 'errorCode') {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: data.errorMsg\r\n    })\r\n  } else if (data.error && data.error.code !== 0) {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n    })\r\n  } else if (data.result && data.result.code !== 'success') {\r\n    if (data.result.code === 'invalidToken') {\r\n      return GoLogin()\r\n    }\r\n  }\r\n  return [false]\r\n}\r\n\r\n// handler method when content type equals text/html\r\nexport const HTMLContentTypeHandler = async (e) => {\r\n  // Determine if it is a login page\r\n  if (e.data.indexOf('action=\"login.do\"') > -1) {\r\n    return GoLogin()\r\n  }\r\n  // Determine if it is a login page\r\n  if (e.request.responseURL.indexOf('login.do') > -1) {\r\n    return GoLogin()\r\n  }\r\n  return [false]\r\n}\r\n", "import axios from \"axios\";\r\nimport qs from \"qs\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTMLContentTypeHandler } from \"./config\";\r\n\r\nif (process.env.NODE_ENV !== \"development\") {\r\n  axios.defaults.withCredentials = true;\r\n}\r\n\r\nexport default async ({ method = \"get\", path, params = null, data = null, contentType }) => {\r\n  try {\r\n    if (process.env.NODE_ENV === \"development\") {\r\n      params = Object.assign({}, params, { appToken: localStorage.getItem(\"user.token\") });\r\n    }\r\n    // 增加随机数参数，解决 IE 缓存\r\n    params = Object.assign({}, params, { r: Math.random() });\r\n    params = method === \"get\" ? Object.assign({}, params, data) : params;\r\n    // init contentType\r\n    let contentTypeString = \"application/json; charset=utf-8\";\r\n    if (contentType === \"json\") {\r\n      contentTypeString = \"application/json; charset=utf-8\";\r\n    } else if (contentType === \"form\") {\r\n      contentTypeString = \"application/x-www-form-urlencoded; charset=utf-8\";\r\n    } else {\r\n      contentTypeString = contentType;\r\n    }\r\n    // serialize data variable\r\n    if (data && contentType === \"form\" && /put|post|patch/.test(method)) {\r\n      data = qs.stringify(data);\r\n    }\r\n\r\n    const res = await axios({\r\n      method: method,\r\n      url: \"/\" + path,\r\n      baseURL:\r\n        process.env.NODE_ENV === \"development\" ? localStorage.getItem(\"server.baseUrl\") : null,\r\n      params: params,\r\n      headers: {\r\n        \"Content-Type\": contentTypeString,\r\n        Accept: \"*/*\",\r\n      },\r\n      data: /put|post|patch/.test(method) ? data : \"\",\r\n    });\r\n    // return login.do page\r\n    if (res.headers[\"content-type\"].indexOf(\"text/html\") > -1) {\r\n      await HTMLContentTypeHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // return error status\r\n\r\n    if (\r\n      (res.data && res.data.code && [\"success\", \"0000\"].indexOf(res.data.code) < 0) ||\r\n      (res.data && res.data.error) ||\r\n      (res.data &&\r\n        res.data.result &&\r\n        res.data.result.code &&\r\n        [\"success\", \"0000\"].indexOf(res.data.result.code) < 0)\r\n    ) {\r\n      await StatusErrorHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // retrun success\r\n    return [true, res.data];\r\n  } catch (e) {\r\n    await StatusErrorHandler(e);\r\n    return [false, e.data];\r\n  }\r\n};\r\n", "import xhr from './axios'\r\n\r\nexport default xhr", "import download from './download'\r\nimport xhr from '../xhr'\r\nimport { Loading } from 'element-ui'\r\nimport { Notification } from 'element-ui'\r\n\r\nfunction downloadAsync (options = {}, attributes = {}) {\r\n  this.loading = Loading.service({lock: true,text: '正在处理下载数据'})\r\n  this.options = R.clone(options)\r\n  this.attributes = attributes\r\n  this.key = ''\r\n  this.filePath = ''\r\n  this.fileName = ''\r\n  this.run()\r\n  return [true]\r\n}\r\n\r\ndownloadAsync.prototype = {\r\n  async run () {\r\n    await this.getFileKey()\r\n    await this.checkProcess()\r\n  },\r\n  updateLoading (text) {\r\n    this.loading.text = text\r\n  },\r\n  closeLoading () {\r\n    this.loading.close()\r\n  },\r\n  async getFileKey () {\r\n    const [status, res] = await xhr(this.options)\r\n    if (status) {      \r\n      this.key = res.progressStatus.key\r\n      this.updateLoading(res.progressStatus.message)\r\n    }\r\n    return [status, res]\r\n  },\r\n  async checkProcess () {\r\n    const [status, res] = await xhr({\r\n      method: 'get',\r\n      path: 'utils/getprocessstatus.do',\r\n      contentType: \"json\",\r\n      data: {key: this.key, random: math.random()}\r\n    })\r\n    if (status && res.progressStatus && res.progressStatus.status === 'success') {\r\n      this.filePath = res.progressStatus.attrs.filePath || this.attributes.filePath\r\n      this.fileName = res.progressStatus.attrs.fileName || this.attributes.fileName\r\n      this.downloadFile()\r\n      return [true]\r\n    } else if (status && res.progressStatus && res.progressStatus.status === 'error') {\r\n      this.closeLoading()\r\n      Notification.error({\r\n        title: '错误提示',\r\n        message: res.progressStatus.message\r\n      })\r\n      return [false]\r\n    } else {\r\n      if (res.progressStatus && res.progressStatus.message) {\r\n        this.updateLoading(res.progressStatus.message)\r\n      }\r\n    }\r\n    setTimeout(() => {\r\n      this.checkProcess()\r\n    }, 5000)\r\n    return [false]\r\n  },\r\n  downloadFile () {\r\n    download({\r\n      path: '/utils/download.do',\r\n      data: {\r\n        filePath: this.filePath,\r\n        fileName: this.fileName,\r\n        deleteFile: true\r\n      },\r\n      options: {\r\n        target: '_self'\r\n      }\r\n    })\r\n    this.closeLoading()\r\n  }\r\n}\r\n// export default\r\nexport default (options, attributes) => {\r\n  return new downloadAsync(options, attributes)\r\n}\r\n", "import xhr from \"@resources/xhr\";\r\nimport download from \"@resources/utils/tools/download\";\r\nimport downloadAsync from \"@resources/utils/tools/download-async\";\r\n\r\nexport default {\r\n  requestByRPC({ method, params }) {\r\n    // eslint-disable-next-line no-console\r\n    console.info(\"Request(RPC): \", { jsonrpc: \"2.0\", id: 2, method, params });\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: { jsonrpc: \"2.0\", id: 2, method, params },\r\n    });\r\n  },\r\n\r\n  requestByDO({ method = \"post\", path, contentType = \"json\", data, params }) {\r\n    // eslint-disable-next-line no-console\r\n    console.info(\"Request(DO):\", { method, path, contentType, data, params });\r\n    return xhr({ method, path, contentType, data, params });\r\n  },\r\n\r\n  download({ path, data, params, options }) {\r\n    // eslint-disable-next-line no-console\r\n    console.info(\"Download:\", { path, data, params, options });\r\n    return download({ path, data, params, options });\r\n  },\r\n\r\n  downloadAsync({ method = \"post\", path, contentType = \"json\", data, params }, attributes = {}) {\r\n    // eslint-disable-next-line no-console\r\n    console.info(\"Download(Async):\", { method, path, contentType, data, params }, attributes);\r\n    return downloadAsync({ method, path, contentType, data, params }, attributes);\r\n  },\r\n};\r\n", "import { Notification } from 'element-ui';\r\n\r\nexport default {\r\n  success: Notification.success,\r\n  info: Notification.info,\r\n  warning: Notification.warning,\r\n  error: Notification.error\r\n}", "import vue from \"vue\";\r\nimport qs from \"qs\";\r\n\r\nexport default ({ path, params = {}, data, options = {} } = {}) => {\r\n  let loading = vue.$loading({\r\n    lock: true,\r\n    text: \"正在下载\",\r\n    spinner: \"el-icon-loading\",\r\n    background: \"rgba(0, 0, 0, 0.7)\",\r\n  });\r\n\r\n  let form = document.createElement(\"form\");\r\n  if (process.env.NODE_ENV === \"development\") {\r\n    params.appToken = localStorage.getItem(\"user.token\");\r\n    path = localStorage.getItem(\"server.baseUrl\") + path;\r\n  }\r\n  params = qs.stringify(params);\r\n  if (params) {\r\n    if (/\\?/g.test(path)) {\r\n      path = `${path}&${params}`;\r\n    } else {\r\n      path = `${path}?${params}`;\r\n    }\r\n  }\r\n  form.setAttribute(\"action\", path);\r\n\r\n  form.setAttribute(\"id\", \"downloadForm\");\r\n  form.setAttribute(\"style\", \"display: none;\");\r\n  form.setAttribute(\"name\", \"downloadForm\");\r\n  form.setAttribute(\"method\", options.method || \"post\");\r\n  form.setAttribute(\"target\", options.target || \"_blank\");\r\n\r\n  document.body.appendChild(form);\r\n\r\n  for (let name in data) {\r\n    if (Object.prototype.toString.call(data[name]) == \"[object Array]\") {\r\n      // 兼容数组参数\r\n      data[name].map((item) => {\r\n        let input = document.createElement(\"input\");\r\n        input.setAttribute(\"type\", \"text\");\r\n        input.setAttribute(\"name\", `${name}`);\r\n        input.setAttribute(\"value\", parseInt(item));\r\n        form.appendChild(input);\r\n      });\r\n    } else {\r\n      if (data[name] === null) continue;\r\n      let input = document.createElement(\"input\");\r\n      input.setAttribute(\"type\", \"text\");\r\n      input.setAttribute(\"name\", name);\r\n      input.setAttribute(\"value\", data[name]);\r\n      form.appendChild(input);\r\n    }\r\n  }\r\n  form.submit();\r\n  document.body.removeChild(form);\r\n\r\n  loading.close();\r\n};\r\n", "import notify from '@utils/dialog/notify'\r\n\r\nexport const Timeout = 20000\r\n\r\nexport const GoLogin = async () => {\r\n  if (process.env.NODE_ENV === 'development') {\r\n    const projectName = window.location.pathname.replace(/\\/([^/]*).*/, '$1')\r\n    if (projectName !== 'dev') {\r\n      return window.location.href = '/dev#/'\r\n    }\r\n  } else {\r\n    return top && (top.location = '/logout.do')\r\n  }\r\n}\r\n\r\nexport const StatusErrorHandler = async (e) => {\r\n  const data = e.data\r\n  if (!e.data) {\r\n    // do nothing\r\n    if (e.message === 'Request failed with status code 502') {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '服务器响应失败，请联系管理人员！'\r\n      })\r\n    } else {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n      })\r\n    }\r\n  } else if (data.code === 'errorCode') {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: data.errorMsg\r\n    })\r\n  } else if (data.error && data.error.code !== 0) {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n    })\r\n  } else if (data.result && data.result.code !== 'success') {\r\n    if (data.result.code === 'invalidToken') {\r\n      return GoLogin()\r\n    }\r\n  }\r\n  return [false]\r\n}\r\n\r\n// handler method when content type equals text/html\r\nexport const HTMLContentTypeHandler = async (e) => {\r\n  // Determine if it is a login page\r\n  if (e.data.indexOf('action=\"login.do\"') > -1) {\r\n    return GoLogin()\r\n  }\r\n  // Determine if it is a login page\r\n  if (e.request.responseURL.indexOf('login.do') > -1) {\r\n    return GoLogin()\r\n  }\r\n  return [false]\r\n}\r\n", "import axios from \"axios\";\r\nimport qs from \"qs\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTMLContentTypeHandler } from \"./config\";\r\n\r\nif (process.env.NODE_ENV !== \"development\") {\r\n  axios.defaults.withCredentials = true;\r\n}\r\n\r\nexport default async ({ method = \"get\", path, params = null, data = null, contentType }) => {\r\n  try {\r\n    if (process.env.NODE_ENV === \"development\") {\r\n      params = Object.assign({}, params, { appToken: localStorage.getItem(\"user.token\") });\r\n    }\r\n    // 增加随机数参数，解决 IE 缓存\r\n    params = Object.assign({}, params, { r: Math.random() });\r\n    params = method === \"get\" ? Object.assign({}, params, data) : params;\r\n    // init contentType\r\n    let contentTypeString = \"application/json; charset=utf-8\";\r\n    if (contentType === \"json\") {\r\n      contentTypeString = \"application/json; charset=utf-8\";\r\n    } else if (contentType === \"form\") {\r\n      contentTypeString = \"application/x-www-form-urlencoded; charset=utf-8\";\r\n    }\r\n    // serialize data variable\r\n    if (data && contentType === \"form\" && /put|post|patch/.test(method)) {\r\n      data = qs.stringify(data);\r\n    }\r\n    const res = await axios({\r\n      method: method,\r\n      url: \"/\" + path,\r\n      baseURL:\r\n        process.env.NODE_ENV === \"development\" ? localStorage.getItem(\"server.baseUrl\") : null,\r\n      params: params,\r\n      headers: {\r\n        \"Content-Type\": contentTypeString,\r\n        Accept: \"*/*\",\r\n      },\r\n      data: /put|post|patch/.test(method) ? data : \"\",\r\n    });\r\n    // return login.do page\r\n    if (res.headers[\"content-type\"].indexOf(\"text/html\") > -1) {\r\n      await HTMLContentTypeHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // return error status\r\n\r\n    if (\r\n      (res.data && res.data.code && [\"success\", \"0000\"].indexOf(res.data.code) < 0) ||\r\n      (res.data && res.data.error) ||\r\n      (res.data &&\r\n        res.data.result &&\r\n        res.data.result.code &&\r\n        [\"success\", \"0000\"].indexOf(res.data.result.code) < 0)\r\n    ) {\r\n      await StatusErrorHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // retrun success\r\n    return [true, res.data];\r\n  } catch (e) {\r\n    await StatusErrorHandler(e);\r\n    return [false, e.data];\r\n  }\r\n};\r\n", "import xhr from './axios'\r\n\r\nexport default xhr", "var map = {\n\t\"./customize/files/register.conf.js\": 9580,\n\t\"./customize/popconfirm/register.conf.js\": 1883,\n\t\"./select/brand/brand-by-channel/register.conf.js\": 5523,\n\t\"./select/dealer/dealer-by-resourceId/register.conf.js\": 3585,\n\t\"./select/dealer/dealer-by-sales/register.conf.js\": 3381,\n\t\"./select/dealer/retailer-by-distributor/register.conf.js\": 7029,\n\t\"./select/dict-options/register.conf.js\": 1704,\n\t\"./select/number/register.conf.js\": 8566,\n\t\"./select/options/register.conf.js\": 5921,\n\t\"./select/region/region-by-resourceId/register.conf.js\": 4920,\n\t\"./select/user/dsr-by-resourceId/register.conf.js\": 2641,\n\t\"./select/user/user-by-resourceId/register.conf.js\": 3229\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 2090;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"js/\" + chunkId + \".\" + {\"23\":\"475e7d4c\",\"49\":\"5c4cbda2\",\"52\":\"76c8fe01\",\"310\":\"6b47d7e0\",\"362\":\"fe96d575\",\"365\":\"dccd11b6\",\"605\":\"75eebef5\",\"837\":\"8d1bee1c\",\"877\":\"fbe2ee40\",\"966\":\"df8459e8\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"362\":\"b154d5d4\",\"365\":\"ec533a38\",\"605\":\"d684f2cb\",\"877\":\"ed7b1483\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "var inProgress = {};\nvar dataWebpackPrefix = \"vue-chevron-desktop:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t};\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"\";", "var createStylesheet = function(chunkId, fullhref, resolve, reject) {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tvar onLinkComplete = function(event) {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + realHref + \")\");\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tlinkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\tdocument.head.appendChild(linkTag);\n\treturn linkTag;\n};\nvar findStylesheet = function(href, fullhref) {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = function(chunkId) {\n\treturn new Promise(function(resolve, reject) {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.miniCss = function(chunkId, promises) {\n\tvar cssChunks = {\"362\":1,\"365\":1,\"605\":1,\"877\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(function() {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, function(e) {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkvue_chevron_desktop\"] = self[\"webpackChunkvue_chevron_desktop\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [998], function() { return __webpack_require__(1002); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["module", "exports", "name", "global", "render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "staticRenderFns", "watch", "$route", "to", "from", "globalConfig", "query", "executor", "test", "path", "salesChannel", "$store", "commit", "component", "titles", "matched", "slice", "for<PERSON>ach", "handler", "title", "meta", "push", "join", "document", "router", "after<PERSON>ach", "docTitleReplacer", "<PERSON><PERSON>", "Router", "mode", "routes", "hooks", "timeInterval", "state", "options", "getters", "getOptions", "dictName", "find", "getOptionsData", "_", "data", "mutations", "UPDATE_OPTIONS", "payload", "updateTime", "Date", "getTime", "item", "Object", "assign", "actions", "getDictOptions", "status", "coreService", "method", "params", "res", "result", "map", "value", "dicItemCode", "label", "dicItemName", "permissions", "getPermission", "permissionName", "permission", "getPermissionData", "hasPermission", "weight", "bit", "math", "log", "parseInt", "isNaN", "hasPermissionNotAdmin", "UPDATE_PERMISSION", "getOperationPermissionByUser", "currentUser", "getCurrentUser", "UPDATE_CURRENT_USER", "getCurrentUserInfo", "Vuex", "modules", "dictOptions", "user", "config", "create", "all", "ElementUI", "Loading", "decimal", "thousands", "prefix", "suffix", "precision", "numberToThousand", "number", "length", "split", "replace", "vue", "val", "Number", "round", "fmt", "dayjs", "format", "option", "R", "require", "RegisterConfs", "addOn", "stores", "window", "keys", "store", "registerModule", "conf", "componentPath", "resolve", "h", "app", "$mount", "convertForm", "form", "brand", "signType", "distributorId", "distributorName", "costCenter", "companyCode", "localMake", "retailerId", "retailerName", "partnerId", "storeId", "storeName", "budgetAmount", "settlementAmount", "vatInvoiceType", "supplierId", "storeInfo", "storeSignInfo", "productInfo", "applyAttFiles", "quoteAmount", "applyBaseInfo", "id", "Service", "getApplyFormById", "xhr", "contentType", "jsonrpc", "stepCode", "getSignboardMaterial", "applyType", "savaApplyForm", "operationApplyForm", "remark", "versionNo", "abortApplyForm", "abortRequest", "comment", "recallRequest", "deleteApplyForm", "getListPermission", "moduleCode", "getSeminarTips", "getSignageTips", "getSignageStoreTips", "getOverallPerformanceTips", "getCostCenter", "getApplyList", "startDate", "date<PERSON><PERSON><PERSON>", "endDate", "limit", "start", "page", "field", "direction", "dealerId", "region", "startApplyTime", "endApplyTime", "getReviewProcess", "getReviewHistory", "exportPDF", "type", "download", "exportExcel", "packageName", "viewName", "fileName", "columnInfoDictKey", "pageType", "getFundDetail", "mktType", "indexOf", "mktId", "getPartnerCkShopTips", "getSuppliers", "getProductsSales", "getDealersByKeyword", "url<PERSON><PERSON>", "partner<PERSON>ame", "resourceId", "getStoreByDealerId", "pageSize", "queryType", "workshopName", "keyword", "funFlag", "fromSource", "businessWeight", "mktKey", "oldMktKey", "pageIndex", "reqNo", "dealerName", "includeDMS", "storeCooperationyear", "storeProvince", "storeCity", "storeRegion", "storeAddress", "storeContact", "storeContacts", "storeType", "storeCubicle", "storeWorkshopId", "storeCustomerType", "signboardMaterial", "signboardStyleFirst", "signboardStyleSecond", "signboardHeight", "signboardWidth", "signboardArea", "signboardQuote", "signboardDoorQuote", "signboardDecorationQuote", "signboardRequirement", "signboardSupplierId", "otherApplyReason", "otherCompleteAmount", "otherSupplierConcact", "otherSupplierConcacts", "completeTime", "attAppliedVehicle", "attCarQrcode", "attOriginalSignboard", "attOriginalOutdoorAdSign", "attQuotation", "attStampedQuotation", "attDesign", "attApplyForm", "attARIBAOrder", "attInvoice", "attConfirmProof", "attTripleAgreement", "attPaymentProof", "attCompletion", "attCarAdQuali", "attInvoiceConfirm", "createTime", "bizPermissionWeight", "unitPriceLimit", "<PERSON><PERSON><PERSON><PERSON>", "abortOperationName", "rejectOperationName", "acceptOperationName", "recallOperationName", "workflowInstance", "formStatus", "conferenceEstimatedDate", "conferenceAddress", "conferenceApplicationFee", "conferenceEstimatedPurchaseVolume", "conferenceNumberOfPeople", "conferencePlace", "conferenceQuote", "conferenceActualPurchaseVolume", "attInvoiceCheck", "attMeetingFlow", "attMeetingLocal", "attMeetingSign", "quoteLimit", "organizationName", "oldData", "customerType", "source", "target", "JSON", "parse", "attId", "fileType", "sourceType", "storageName", "storePath", "remoteUrl", "products", "toRequestAttFormat", "stringify", "salesInfo", "Math", "undefined", "signage", "LocalSignage", "seminar", "LocalSeminar", "ck", "LocalCk", "applyForm", "hasAuthInBiz", "CLEAR_APPLY_FORM", "clone", "SET_STORE_INFO", "workshopAddress", "<PERSON><PERSON><PERSON>", "contactPersonTel", "provinceName", "cityName", "seatsNum", "regionName", "SET_CK_STORE_INFO", "dispatch", "applyService", "toSignageLocal", "merge", "currentStep", "toCkLocal", "toSeminarLocal", "sales", "toSignageRequest", "RequestSignage", "toCkRequest", "RequestCk", "toSeminarRequest", "RequestSeminar", "ckProducts", "rootState", "list", "observer", "index", "abortByStep", "recallByRequest", "todo", "filter", "tableData", "initCkProducts", "show", "total", "loading", "loadingText", "signboardStyle", "appliedVehicle", "SHOW_DIALOG", "dialogName", "CLEAR_STORE_LIST", "HIDE_DIALOG", "commonService", "resultLst", "previewForm", "SET_GLOBAL", "done", "searchParams", "pageParams", "permissionWeight", "hasAuthInList", "getListByDone", "getListByObserver", "categoryName", "estimatedPack", "actualPack", "category", "initProducts", "packageProducts", "deloProducts", "totalActualPack", "tableItem", "dataItem", "productCategory", "rolling12MonthSellIn", "add", "bignumber", "valueOf", "apply", "dialog", "GoL<PERSON>in", "top", "location", "StatusErrorHandler", "e", "code", "notify", "duration", "message", "errorMsg", "error", "HTMLContentTypeHandler", "request", "responseURL", "axios", "process", "r", "random", "contentTypeString", "qs", "url", "baseURL", "headers", "Accept", "downloadAsync", "attributes", "lock", "text", "key", "filePath", "run", "prototype", "getFile<PERSON>ey", "checkProcess", "updateLoading", "closeLoading", "close", "progressStatus", "downloadFile", "Notification", "setTimeout", "deleteFile", "requestByRPC", "console", "info", "requestByDO", "success", "warning", "spinner", "background", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "toString", "call", "input", "submit", "<PERSON><PERSON><PERSON><PERSON>", "webpackContext", "req", "webpackContextResolve", "__webpack_require__", "o", "Error", "__webpack_module_cache__", "moduleId", "cachedModule", "__webpack_modules__", "m", "deferred", "O", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "every", "splice", "n", "getter", "__esModule", "d", "a", "definition", "defineProperty", "enumerable", "get", "f", "chunkId", "Promise", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "Function", "obj", "prop", "hasOwnProperty", "inProgress", "dataWebpackPrefix", "l", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "s", "getAttribute", "charset", "timeout", "nc", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "bind", "head", "Symbol", "toStringTag", "p", "createStylesheet", "fullhref", "reject", "linkTag", "rel", "onLinkComplete", "errorType", "realHref", "href", "err", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "tag", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "then", "installedChunks", "installedChunkData", "promise", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}