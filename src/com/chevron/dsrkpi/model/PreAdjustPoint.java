package com.chevron.dsrkpi.model;

import java.util.Date;

/**
 * Pre Adjust Point-wx_t_pre_adjust_point
 * <AUTHOR>
 * @version 1.0 2020-05-07 11:03
 */
public class PreAdjustPoint {

	/**  ID  **/
    private Long id;

	/**  销售ID  **/
    private Long dsrId;

	/**  Pre Points  **/
    private Double prePoints;

	/**  Dic Type Code  **/
    private String dicTypeCode;

	/**  Dic Item Code  **/
    private String dicItemCode;

	/**  Organization Name  **/
    private String organizationName;

	/**  Trans Time  **/
    private String transTime;

	/**  状态(0-为同步, 1-已同步)  **/
    private Integer status;

	/**  Del Flage  **/
    private Integer delFlage;

	/**  最后修改时间  **/
    private Date updateTime;

	/**  最后修改用户ID  **/
    private Long updateUserId;

	/**  创建时间  **/
    private Date createTime;

	/**  创建用户ID  **/
    private Long createUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDsrId() {
        return dsrId;
    }

    public void setDsrId(Long dsrId) {
        this.dsrId = dsrId;
    }

    public Double getPrePoints() {
        return prePoints;
    }

    public void setPrePoints(Double prePoints) {
        this.prePoints = prePoints;
    }

    public String getDicTypeCode() {
        return dicTypeCode;
    }

    public void setDicTypeCode(String dicTypeCode) {
        this.dicTypeCode = dicTypeCode;
    }

    public String getDicItemCode() {
        return dicItemCode;
    }

    public void setDicItemCode(String dicItemCode) {
        this.dicItemCode = dicItemCode;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getTransTime() {
        return transTime;
    }

    public void setTransTime(String transTime) {
        this.transTime = transTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDelFlage() {
        return delFlage;
    }

    public void setDelFlage(Integer delFlage) {
        this.delFlage = delFlage;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }
}
